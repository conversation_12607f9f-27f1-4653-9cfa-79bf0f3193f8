import { Metadata } from 'next'

export interface ReviewFrontMatter {
  title: string
  summary: string
  rating: number
  address: string
  lat: number
  lng: number
  priceLevel: string
  cuisine: string
  datePublished: string
  lastUpdated: string
  slug?: string
  image?: string
  author?: string
}

export interface SiteConfig {
  name: string
  description: string
  url: string
  ogImage: string
  author: {
    name: string
    url: string
    email: string
  }
  social: {
    twitter: string
    instagram: string
    facebook: string
  }
}

export const siteConfig: SiteConfig = {
  name: "HFX Eats",
  description: "Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.",
  url: "https://hfxeats.com",
  ogImage: "https://hfxeats.com/og-image.jpg",
  author: {
    name: "Halifax Food Critic",
    url: "https://hfxeats.com/about",
    email: "<EMAIL>"
  },
  social: {
    twitter: "@hfxeats",
    instagram: "@hfxeats",
    facebook: "hfxeats"
  }
}

export function generateMetadata(frontMatter: ReviewFrontMatter, type: 'review' | 'page' = 'review'): Metadata {
  const { title, summary, slug, image } = frontMatter
  const url = `${siteConfig.url}/${type === 'review' ? 'reviews' : ''}/${slug}/`
  const ogImage = image ? `${siteConfig.url}/images/${slug}/${image}` : siteConfig.ogImage

  return {
    title: `${title} | ${siteConfig.name}`,
    description: summary,
    authors: [{ name: siteConfig.author.name, url: siteConfig.author.url }],
    creator: siteConfig.author.name,
    publisher: siteConfig.name,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(siteConfig.url),
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description: summary,
      url,
      siteName: siteConfig.name,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'en_CA',
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description: summary,
      images: [ogImage],
      creator: siteConfig.social.twitter,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  }
}

export function generateReviewStructuredData(frontMatter: ReviewFrontMatter) {
  const { title, summary, rating, address, datePublished, lastUpdated, slug, author = siteConfig.author.name } = frontMatter
  
  return {
    '@context': 'https://schema.org',
    '@graph': [
      {
        '@type': 'Review',
        '@id': `${siteConfig.url}/reviews/${slug}/#review`,
        headline: title,
        description: summary,
        datePublished,
        dateModified: lastUpdated,
        author: {
          '@type': 'Person',
          name: author,
          url: siteConfig.author.url,
        },
        publisher: {
          '@type': 'Organization',
          name: siteConfig.name,
          url: siteConfig.url,
        },
        reviewRating: {
          '@type': 'Rating',
          ratingValue: rating,
          bestRating: 5,
          worstRating: 1,
        },
        itemReviewed: {
          '@type': 'Restaurant',
          name: title.replace(' Review', '').replace(' – Worth the Hype in 2025?', ''),
          address: {
            '@type': 'PostalAddress',
            streetAddress: address,
            addressLocality: 'Halifax',
            addressRegion: 'NS',
            addressCountry: 'CA',
          },
          servesCuisine: frontMatter.cuisine,
          priceRange: frontMatter.priceLevel,
        },
      },
      {
        '@type': 'BlogPosting',
        '@id': `${siteConfig.url}/reviews/${slug}/#blogpost`,
        headline: title,
        description: summary,
        datePublished,
        dateModified: lastUpdated,
        author: {
          '@type': 'Person',
          name: author,
          url: siteConfig.author.url,
        },
        publisher: {
          '@type': 'Organization',
          name: siteConfig.name,
          url: siteConfig.url,
          logo: {
            '@type': 'ImageObject',
            url: `${siteConfig.url}/logo.png`,
          },
        },
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': `${siteConfig.url}/reviews/${slug}/`,
        },
      },
    ],
  }
}

export function generateLocalBusinessStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': `${siteConfig.url}/#localbusiness`,
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Halifax',
      addressRegion: 'NS',
      addressCountry: 'CA',
    },
    areaServed: {
      '@type': 'City',
      name: 'Halifax',
      addressRegion: 'NS',
      addressCountry: 'CA',
    },
    knowsAbout: [
      'Restaurant Reviews',
      'Halifax Dining',
      'Nova Scotia Restaurants',
      'Food Criticism',
      'Culinary Experiences',
    ],
  }
}

export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }
}
