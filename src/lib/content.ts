import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import readingTime from 'reading-time'
import { ReviewFrontMatter } from './seo'

const contentDirectory = path.join(process.cwd(), 'content')
const reviewsDirectory = path.join(contentDirectory, 'reviews')

export interface ReviewPost extends ReviewFrontMatter {
  slug: string
  content: string
  readingTime: {
    text: string
    minutes: number
    time: number
    words: number
  }
}

export function getAllReviewSlugs(): string[] {
  if (!fs.existsSync(reviewsDirectory)) {
    return []
  }
  
  const files = fs.readdirSync(reviewsDirectory)
  return files
    .filter((file) => file.endsWith('.mdx'))
    .map((file) => file.replace(/\.mdx$/, ''))
}

export function getReviewBySlug(slug: string): ReviewPost | null {
  try {
    const fullPath = path.join(reviewsDirectory, `${slug}.mdx`)
    
    if (!fs.existsSync(fullPath)) {
      return null
    }
    
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)
    const readingTimeResult = readingTime(content)

    return {
      slug,
      content,
      readingTime: readingTimeResult,
      title: data.title || '',
      summary: data.summary || '',
      rating: data.rating || 0,
      address: data.address || '',
      lat: data.lat || 0,
      lng: data.lng || 0,
      priceLevel: data.priceLevel || '',
      cuisine: data.cuisine || '',
      datePublished: data.datePublished || '',
      lastUpdated: data.lastUpdated || data.datePublished || '',
      image: data.image || '',
      author: data.author || '',
    }
  } catch (error) {
    console.error(`Error reading review ${slug}:`, error)
    return null
  }
}

export function getAllReviews(): ReviewPost[] {
  const slugs = getAllReviewSlugs()
  const reviews = slugs
    .map((slug) => getReviewBySlug(slug))
    .filter((review): review is ReviewPost => review !== null)
    .sort((a, b) => {
      // Sort by date published, newest first
      return new Date(b.datePublished).getTime() - new Date(a.datePublished).getTime()
    })

  return reviews
}

export function getReviewsByRating(minRating: number = 4): ReviewPost[] {
  return getAllReviews().filter((review) => review.rating >= minRating)
}

export function getReviewsByCuisine(cuisine: string): ReviewPost[] {
  return getAllReviews().filter((review) => 
    review.cuisine.toLowerCase().includes(cuisine.toLowerCase())
  )
}

export function getReviewsByPriceLevel(priceLevel: string): ReviewPost[] {
  return getAllReviews().filter((review) => review.priceLevel === priceLevel)
}

export function getRelatedReviews(currentSlug: string, limit: number = 3): ReviewPost[] {
  const currentReview = getReviewBySlug(currentSlug)
  if (!currentReview) return []

  const allReviews = getAllReviews().filter((review) => review.slug !== currentSlug)
  
  // Score reviews based on similarity
  const scoredReviews = allReviews.map((review) => {
    let score = 0
    
    // Same cuisine gets high score
    if (review.cuisine === currentReview.cuisine) score += 3
    
    // Similar price level gets medium score
    if (review.priceLevel === currentReview.priceLevel) score += 2
    
    // Similar rating gets low score
    if (Math.abs(review.rating - currentReview.rating) <= 0.5) score += 1
    
    return { review, score }
  })
  
  // Sort by score and return top results
  return scoredReviews
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map((item) => item.review)
}

export function searchReviews(query: string): ReviewPost[] {
  const allReviews = getAllReviews()
  const lowercaseQuery = query.toLowerCase()
  
  return allReviews.filter((review) => {
    return (
      review.title.toLowerCase().includes(lowercaseQuery) ||
      review.summary.toLowerCase().includes(lowercaseQuery) ||
      review.cuisine.toLowerCase().includes(lowercaseQuery) ||
      review.address.toLowerCase().includes(lowercaseQuery)
    )
  })
}

// Utility function to format date for display
export function formatDate(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

// Utility function to get average rating
export function getAverageRating(): number {
  const reviews = getAllReviews()
  if (reviews.length === 0) return 0
  
  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
  return Math.round((totalRating / reviews.length) * 10) / 10
}
