'use client'

import { MapPin } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MapEmbedProps {
  lat: number
  lng: number
  address?: string
  className?: string
  height?: string
}

export function MapEmbed({ 
  lat, 
  lng, 
  address, 
  className,
  height = 'h-64'
}: MapEmbedProps) {
  // Create Google Maps embed URL
  const mapUrl = `https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${lat},${lng}&zoom=15`
  
  // Fallback to Google Maps link if no API key
  const fallbackUrl = `https://www.google.com/maps?q=${lat},${lng}`

  return (
    <div className={cn('my-8 space-y-4', className)}>
      <div className="flex items-center gap-2 text-lg font-semibold">
        <MapPin className="w-5 h-5 text-primary" />
        Location
      </div>
      
      {address && (
        <p className="text-gray-600 dark:text-gray-400">{address}</p>
      )}
      
      <div className={cn('relative rounded-lg overflow-hidden border', height)}>
        {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? (
          <iframe
            src={mapUrl}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title={`Map showing location ${address ? `of ${address}` : `at ${lat}, ${lng}`}`}
          />
        ) : (
          <div className="w-full h-full bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center space-y-4">
            <MapPin className="w-12 h-12 text-gray-400" />
            <div className="text-center space-y-2">
              <p className="text-gray-600 dark:text-gray-400">
                Interactive map not available
              </p>
              <a
                href={fallbackUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-primary hover:underline"
              >
                <MapPin className="w-4 h-4" />
                View on Google Maps
              </a>
            </div>
          </div>
        )}
      </div>
      
      <div className="flex justify-center">
        <a
          href={fallbackUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-2 text-sm text-primary hover:underline"
        >
          <MapPin className="w-4 h-4" />
          Open in Google Maps
        </a>
      </div>
    </div>
  )
}
