import { Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProsConsProps {
  pros: string[]
  cons: string[]
  className?: string
}

export function ProsCons({ pros, cons, className }: ProsConsProps) {
  return (
    <div className={cn('grid md:grid-cols-2 gap-6 my-8', className)}>
      {/* Pros Section */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-green-700 dark:text-green-400 flex items-center gap-2">
          <Check className="w-5 h-5" />
          What We Loved
        </h3>
        <ul className="space-y-2">
          {pros.map((pro, index) => (
            <li key={index} className="flex items-start gap-3">
              <Check className="w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-300">{pro}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Cons Section */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 flex items-center gap-2">
          <X className="w-5 h-5" />
          Room for Improvement
        </h3>
        <ul className="space-y-2">
          {cons.map((con, index) => (
            <li key={index} className="flex items-start gap-3">
              <X className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-300">{con}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
