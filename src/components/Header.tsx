import Link from 'next/link'
import { Search, Menu } from 'lucide-react'
import { siteConfig } from '@/lib/seo'

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="text-2xl font-bold text-primary">
              {siteConfig.name}
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              href="/reviews/" 
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Reviews
            </Link>
            <Link 
              href="/guides/" 
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Guides
            </Link>
            <Link 
              href="/neighbourhoods/" 
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Neighbourhoods
            </Link>
            <Link 
              href="/about/" 
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              About
            </Link>
          </nav>

          {/* Search and Mobile Menu */}
          <div className="flex items-center space-x-4">
            <button
              className="p-2 hover:bg-accent rounded-md transition-colors"
              aria-label="Search"
            >
              <Search className="h-4 w-4" />
            </button>
            
            <button
              className="md:hidden p-2 hover:bg-accent rounded-md transition-colors"
              aria-label="Menu"
            >
              <Menu className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}
