import Link from 'next/link'
import Image from 'next/image'
import { Calendar, MapPin, Clock } from 'lucide-react'
import { RatingStars } from './RatingStars'
import { ReviewPost } from '@/lib/content'
import { formatDate } from '@/lib/content'
import { cn } from '@/lib/utils'

interface ReviewCardProps {
  review: ReviewPost
  className?: string
  priority?: boolean
}

export function ReviewCard({ review, className, priority = false }: ReviewCardProps) {
  const imageUrl = review.image 
    ? `/images/${review.slug}/${review.image}`
    : '/images/placeholder-restaurant.jpg'

  return (
    <article className={cn(
      'group bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow duration-200',
      className
    )}>
      <Link href={`/reviews/${review.slug}/`} className="block">
        <div className="aspect-video relative overflow-hidden rounded-t-lg">
          <Image
            src={imageUrl}
            alt={review.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-200"
            priority={priority}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute top-3 right-3">
            <div className="bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full px-2 py-1">
              <RatingStars rating={review.rating} size="sm" showNumber={false} />
            </div>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <h3 className="text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors">
              {review.title}
            </h3>
            <p className="text-muted-foreground line-clamp-3">
              {review.summary}
            </p>
          </div>
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <span className="truncate max-w-[120px]">
                  {review.address.split(',')[0]}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <span className="font-medium">{review.priceLevel}</span>
              </div>
            </div>
            <RatingStars rating={review.rating} size="sm" />
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(review.datePublished)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{review.readingTime.text}</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {review.cuisine.split(',').map((cuisine, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground"
              >
                {cuisine.trim()}
              </span>
            ))}
          </div>
        </div>
      </Link>
    </article>
  )
}
