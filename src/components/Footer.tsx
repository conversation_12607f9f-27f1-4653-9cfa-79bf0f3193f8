import Link from 'next/link'
import { Instagram, Twitter, Facebook, Mail } from 'lucide-react'
import { siteConfig } from '@/lib/seo'

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="text-2xl font-bold text-primary">
              {siteConfig.name}
            </div>
            <p className="text-sm text-muted-foreground">
              Halifax&apos;s premier restaurant review blog featuring authentic dining experiences across Nova Scotia&apos;s capital city.
            </p>
            <div className="flex space-x-4">
              <a
                href={`https://twitter.com/${siteConfig.social.twitter.replace('@', '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href={`https://instagram.com/${siteConfig.social.instagram.replace('@', '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href={`https://facebook.com/${siteConfig.social.facebook}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href={`mailto:${siteConfig.author.email}`}
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Email"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Reviews */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Reviews</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/reviews/" className="text-muted-foreground hover:text-primary transition-colors">
                  All Reviews
                </Link>
              </li>
              <li>
                <Link href="/reviews/?rating=5" className="text-muted-foreground hover:text-primary transition-colors">
                  5-Star Reviews
                </Link>
              </li>
              <li>
                <Link href="/reviews/?cuisine=seafood" className="text-muted-foreground hover:text-primary transition-colors">
                  Seafood
                </Link>
              </li>
              <li>
                <Link href="/reviews/?price=$$$" className="text-muted-foreground hover:text-primary transition-colors">
                  Fine Dining
                </Link>
              </li>
            </ul>
          </div>

          {/* Neighbourhoods */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Neighbourhoods</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/neighbourhoods/downtown/" className="text-muted-foreground hover:text-primary transition-colors">
                  Downtown
                </Link>
              </li>
              <li>
                <Link href="/neighbourhoods/north-end/" className="text-muted-foreground hover:text-primary transition-colors">
                  North End
                </Link>
              </li>
              <li>
                <Link href="/neighbourhoods/south-end/" className="text-muted-foreground hover:text-primary transition-colors">
                  South End
                </Link>
              </li>
              <li>
                <Link href="/neighbourhoods/dartmouth/" className="text-muted-foreground hover:text-primary transition-colors">
                  Dartmouth
                </Link>
              </li>
            </ul>
          </div>

          {/* About */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">About</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about/" className="text-muted-foreground hover:text-primary transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/editorial-guidelines/" className="text-muted-foreground hover:text-primary transition-colors">
                  Editorial Guidelines
                </Link>
              </li>
              <li>
                <Link href="/contact/" className="text-muted-foreground hover:text-primary transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/privacy/" className="text-muted-foreground hover:text-primary transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
          <p>
            © {currentYear} {siteConfig.name}. All rights reserved. Made with ❤️ in Halifax, Nova Scotia.
          </p>
        </div>
      </div>
    </footer>
  )
}
