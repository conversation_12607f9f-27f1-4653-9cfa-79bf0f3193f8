import { Star } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RatingStarsProps {
  rating: number
  maxRating?: number
  size?: 'sm' | 'md' | 'lg'
  showNumber?: boolean
  className?: string
}

export function RatingStars({ 
  rating, 
  maxRating = 5, 
  size = 'md', 
  showNumber = true,
  className 
}: RatingStarsProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className="flex items-center gap-0.5">
        {Array.from({ length: maxRating }, (_, i) => {
          const starValue = i + 1
          const isFilled = starValue <= Math.floor(rating)
          const isHalfFilled = starValue === Math.ceil(rating) && rating % 1 !== 0
          
          return (
            <div key={i} className="relative">
              <Star
                className={cn(
                  sizeClasses[size],
                  'text-gray-300 dark:text-gray-600'
                )}
                fill="currentColor"
              />
              {(isFilled || isHalfFilled) && (
                <Star
                  className={cn(
                    sizeClasses[size],
                    'absolute top-0 left-0 text-yellow-400',
                    isHalfFilled && 'clip-path-half'
                  )}
                  fill="currentColor"
                  style={
                    isHalfFilled
                      ? { clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)' }
                      : undefined
                  }
                />
              )}
            </div>
          )
        })}
      </div>
      {showNumber && (
        <span className={cn(
          'font-medium text-gray-900 dark:text-gray-100',
          textSizeClasses[size]
        )}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  )
}
