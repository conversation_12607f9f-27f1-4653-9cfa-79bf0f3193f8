import { ReviewCard } from './ReviewCard'
import { getRelatedReviews } from '@/lib/content'

interface RelatedReviewsProps {
  currentSlug: string
  limit?: number
}

export function RelatedReviews({ currentSlug, limit = 3 }: RelatedReviewsProps) {
  const relatedReviews = getRelatedReviews(currentSlug, limit)

  if (relatedReviews.length === 0) {
    return null
  }

  return (
    <section className="mt-16 space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">You Might Also Like</h2>
        <p className="text-muted-foreground">
          More great dining experiences in Halifax
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {relatedReviews.map((review) => (
          <ReviewCard key={review.slug} review={review} />
        ))}
      </div>
    </section>
  )
}
