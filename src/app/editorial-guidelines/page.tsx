import { Metadata } from 'next'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Editorial Guidelines',
  description: 'Learn about HFX Eats&apos; editorial standards, review process, and commitment to authentic, unbiased restaurant criticism.',
}

export default function EditorialGuidelinesPage() {
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <div className="space-y-16">
        {/* Header */}
        <div className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold">
            Editorial Guidelines
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our commitment to honest, helpful, and authentic restaurant reviews that serve Halifax diners and the local food community.
          </p>
        </div>

        {/* Core Principles */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold">Core Principles</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-4 p-6 border rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <h3 className="text-lg font-semibold">Independence</h3>
              <p className="text-sm text-muted-foreground">
                We maintain complete editorial independence. All meals are paid for by HFX Eats, and we never accept free meals, 
                gifts, or compensation that could influence our reviews.
              </p>
            </div>
            
            <div className="space-y-4 p-6 border rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <h3 className="text-lg font-semibold">Authenticity</h3>
              <p className="text-sm text-muted-foreground">
                Every review reflects genuine dining experiences. We visit restaurants as regular customers and base our 
                opinions on multiple visits when possible.
              </p>
            </div>
            
            <div className="space-y-4 p-6 border rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <h3 className="text-lg font-semibold">Constructive Feedback</h3>
              <p className="text-sm text-muted-foreground">
                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback 
                while celebrating what makes Halifax's food scene special.
              </p>
            </div>
          </div>
        </section>

        {/* Review Process */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold">Review Process</h2>
          
          <div className="space-y-6">
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                1
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Initial Visit</h3>
                <p className="text-muted-foreground">
                  We visit restaurants as regular customers, making reservations under personal names. We never identify ourselves 
                  as reviewers during the dining experience.
                </p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                2
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Documentation</h3>
                <p className="text-muted-foreground">
                  We take detailed notes during and immediately after each visit, documenting food quality, service, 
                  atmosphere, and value. Photos are taken discreetly when appropriate.
                </p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                3
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Multiple Visits</h3>
                <p className="text-muted-foreground">
                  For comprehensive reviews, we aim for multiple visits at different times and days to ensure consistency. 
                  Single-visit reviews are clearly noted.
                </p>
              </div>
            </div>
            
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                4
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Writing & Review</h3>
                <p className="text-muted-foreground">
                  Reviews are written within 48 hours of the final visit while memories are fresh. Each review undergoes 
                  editorial review for accuracy, fairness, and clarity.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Rating System */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold">Rating System</h2>
          
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">5.0</div>
              <div>
                <h3 className="font-semibold">Exceptional</h3>
                <p className="text-sm text-muted-foreground">
                  Outstanding in every aspect. A destination restaurant that defines Halifax dining excellence.
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">4.0-4.9</div>
              <div>
                <h3 className="font-semibold">Excellent</h3>
                <p className="text-sm text-muted-foreground">
                  Highly recommended with minor flaws. Consistently delivers great experiences.
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">3.0-3.9</div>
              <div>
                <h3 className="font-semibold">Good</h3>
                <p className="text-sm text-muted-foreground">
                  Solid choice with some standout elements. Worth visiting with realistic expectations.
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">2.0-2.9</div>
              <div>
                <h3 className="font-semibold">Fair</h3>
                <p className="text-sm text-muted-foreground">
                  Has potential but significant issues prevent recommendation. May improve with time.
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">1.0-1.9</div>
              <div>
                <h3 className="font-semibold">Poor</h3>
                <p className="text-sm text-muted-foreground">
                  Fundamental problems that significantly impact the dining experience. Not recommended.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do/Don't Do */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold">What We Do & Don't Do</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-green-600 flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                We Do
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Pay for all meals and experiences
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Visit restaurants multiple times when possible
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Consider context (new restaurants, off nights, etc.)
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Update reviews when restaurants make significant changes
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Highlight local sourcing and sustainability efforts
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-red-600 flex items-center gap-2">
                <XCircle className="w-5 h-5" />
                We Don't Do
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  Accept free meals or compensation
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  Review restaurants based on single bad experiences
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  Make personal attacks on staff or owners
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  Review restaurants during their first week of operation
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  Use AI-generated content or stock photos
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Corrections */}
        <section className="bg-muted/50 rounded-lg p-8 space-y-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-6 h-6 text-amber-600" />
            <h2 className="text-2xl font-bold">Corrections & Updates</h2>
          </div>
          <p className="text-muted-foreground">
            We strive for accuracy in all our reviews. If you notice an error or if a restaurant has made significant changes 
            since our review, please contact us at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>. 
            We will investigate and update our content as appropriate.
          </p>
          <p className="text-muted-foreground">
            All corrections and significant updates are noted at the bottom of reviews with timestamps for transparency.
          </p>
        </section>
      </div>
    </div>
  )
}
