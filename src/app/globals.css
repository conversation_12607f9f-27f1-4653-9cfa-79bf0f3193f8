@import "tailwindcss";

@theme {
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;
  --color-primary: #3b82f6;
  --color-primary-foreground: #f8fafc;
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #f8fafc;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #3b82f6;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: #0f172a;
    --color-foreground: #f8fafc;
    --color-card: #0f172a;
    --color-card-foreground: #f8fafc;
    --color-popover: #0f172a;
    --color-popover-foreground: #f8fafc;
    --color-primary: #60a5fa;
    --color-primary-foreground: #0f172a;
    --color-secondary: #1e293b;
    --color-secondary-foreground: #f8fafc;
    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;
    --color-accent: #1e293b;
    --color-accent-foreground: #f8fafc;
    --color-destructive: #dc2626;
    --color-destructive-foreground: #f8fafc;
    --color-border: #1e293b;
    --color-input: #1e293b;
    --color-ring: #60a5fa;
  }
}

body {
  font-feature-settings: "rlig" 1, "calt" 1;
}
