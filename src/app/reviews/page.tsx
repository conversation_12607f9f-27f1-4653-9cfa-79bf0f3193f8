import { Metadata } from 'next'
import { getAllReviews, getAverageRating } from '@/lib/content'
import { ReviewCard } from '@/components/ReviewCard'
import { RatingStars } from '@/components/RatingStars'
import { siteConfig } from '@/lib/seo'

export const metadata: Metadata = {
  title: 'Restaurant Reviews',
  description: 'Browse all our Halifax restaurant reviews. Discover the best dining experiences across Nova Scotia&apos;s capital city with honest, detailed reviews.',
  openGraph: {
    title: 'Halifax Restaurant Reviews | HFX Eats',
    description: 'Browse all our Halifax restaurant reviews. Discover the best dining experiences across Nova Scotia&apos;s capital city.',
  },
}

export default function ReviewsPage() {
  const allReviews = getAllReviews()
  const averageRating = getAverageRating()
  const totalReviews = allReviews.length

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Header */}
      <div className="text-center space-y-6">
        <h1 className="text-4xl md:text-5xl font-bold">
          Halifax Restaurant Reviews
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Honest, detailed reviews of Halifax's dining scene. From hidden gems to established favorites, 
          discover your next great meal with our local expertise.
        </p>
        
        <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm">
          <div className="flex items-center gap-2">
            <RatingStars rating={averageRating} size="sm" showNumber={false} />
            <span className="font-medium">{averageRating}/5 Average Rating</span>
          </div>
          <div className="font-medium">
            {totalReviews} Restaurant{totalReviews !== 1 ? 's' : ''} Reviewed
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 justify-center">
        <button className="px-4 py-2 rounded-full bg-primary text-primary-foreground text-sm font-medium">
          All Reviews
        </button>
        <button className="px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent">
          5 Stars
        </button>
        <button className="px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent">
          Seafood
        </button>
        <button className="px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent">
          Fine Dining
        </button>
        <button className="px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent">
          Casual
        </button>
      </div>

      {/* Reviews Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {allReviews.map((review, index) => (
          <ReviewCard 
            key={review.slug} 
            review={review}
            priority={index < 6}
          />
        ))}
      </div>

      {/* Empty State */}
      {allReviews.length === 0 && (
        <div className="text-center py-16 space-y-4">
          <h2 className="text-2xl font-semibold">No Reviews Yet</h2>
          <p className="text-muted-foreground">
            We're working on bringing you the best Halifax restaurant reviews. Check back soon!
          </p>
        </div>
      )}
    </div>
  )
}
