import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { MDXRemote } from 'next-mdx-remote/rsc'
import { getReviewBySlug, getAllReviewSlugs, formatDate } from '@/lib/content'
import { generateMetadata as generateSEOMetadata, generateReviewStructuredData, generateBreadcrumbStructuredData } from '@/lib/seo'
import { RatingStars } from '@/components/RatingStars'
import { ProsCons } from '@/components/ProsCons'
import { MapEmbed } from '@/components/MapEmbed'
import { RelatedReviews } from '@/components/RelatedReviews'
import { Calendar, Clock, MapPin, DollarSign } from 'lucide-react'
import Image from 'next/image'

// MDX Components
const components = {
  ProsCons,
  MapEmbed,
  RatingStars,
}

interface ReviewPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  const slugs = getAllReviewSlugs()
  return slugs.map((slug) => ({
    slug,
  }))
}

export async function generateMetadata({ params }: ReviewPageProps): Promise<Metadata> {
  const { slug } = await params
  const review = getReviewBySlug(slug)

  if (!review) {
    return {
      title: 'Review Not Found',
    }
  }

  return generateSEOMetadata(review, 'review')
}

export default async function ReviewPage({ params }: ReviewPageProps) {
  const { slug } = await params
  const review = getReviewBySlug(slug)

  if (!review) {
    notFound()
  }

  const structuredData = generateReviewStructuredData(review)
  const breadcrumbData = generateBreadcrumbStructuredData([
    { name: 'Home', url: '/' },
    { name: 'Reviews', url: '/reviews/' },
    { name: review.title, url: `/reviews/${review.slug}/` },
  ])

  const imageUrl = review.image 
    ? `/images/${review.slug}/${review.image}`
    : '/images/placeholder-restaurant.jpg'

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbData),
        }}
      />
      
      <article className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <header className="space-y-6 mb-12">
          <div className="space-y-4">
            <h1 className="text-4xl md:text-5xl font-bold leading-tight">
              {review.title}
            </h1>
            
            {/* Quick Take Summary */}
            <div className="bg-muted/50 rounded-lg p-6 border-l-4 border-primary">
              <h2 className="text-lg font-semibold mb-2">Quick Take</h2>
              <p className="text-muted-foreground leading-relaxed">
                {review.summary}
              </p>
            </div>
          </div>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>Published {formatDate(review.datePublished)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>{review.readingTime.text}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span>{review.address}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span>{review.priceLevel}</span>
            </div>
          </div>

          {/* Rating and Cuisine */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <RatingStars rating={review.rating} size="lg" />
            <div className="flex flex-wrap gap-2">
              {review.cuisine.split(',').map((cuisine, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary text-secondary-foreground"
                >
                  {cuisine.trim()}
                </span>
              ))}
            </div>
          </div>
        </header>

        {/* Featured Image */}
        {review.image && (
          <div className="relative aspect-video mb-12 rounded-lg overflow-hidden">
            <Image
              src={imageUrl}
              alt={review.title}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
            />
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg max-w-none dark:prose-invert">
          <MDXRemote source={review.content} components={components} />
        </div>

        {/* Related Reviews */}
        <RelatedReviews currentSlug={review.slug} />
      </article>
    </>
  )
}
