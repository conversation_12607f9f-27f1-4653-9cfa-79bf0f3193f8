import { Metadata } from 'next'
import { siteConfig } from '@/lib/seo'
import { MapPin, Clock, Star, Users } from 'lucide-react'

export const metadata: Metadata = {
  title: 'About Us',
  description: 'Learn about HFX Eats, Halifax&apos;s premier restaurant review blog. Meet our team of local food critics and discover our commitment to authentic dining experiences.',
}

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <div className="space-y-16">
        {/* Header */}
        <div className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold">
            About HFX Eats
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Halifax&apos;s most trusted restaurant review blog, dedicated to uncovering the best dining experiences
            across Nova Scotia&apos;s vibrant capital city.
          </p>
        </div>

        {/* Mission */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold">Our Mission</h2>
          <div className="prose prose-lg max-w-none dark:prose-invert">
            <p>
              <strong>Quick Take:</strong> We&apos;re Halifax locals with deep culinary knowledge and years of restaurant experience.
              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.
            </p>
            <p>
              Founded in 2020, HFX Eats emerged from a simple belief: Halifax deserves thoughtful, honest restaurant criticism 
              that celebrates our unique Maritime culinary identity while holding establishments to high standards. We&apos;re not just reviewers –
              we&apos;re passionate advocates for Halifax&apos;s incredible food scene.
            </p>
          </div>
        </section>

        {/* Stats */}
        <section className="grid md:grid-cols-4 gap-8 py-8 border-y">
          <div className="text-center space-y-2">
            <div className="flex justify-center">
              <Star className="w-8 h-8 text-yellow-400 fill-current" />
            </div>
            <div className="text-2xl font-bold">200+</div>
            <div className="text-sm text-muted-foreground">Restaurants Reviewed</div>
          </div>
          <div className="text-center space-y-2">
            <div className="flex justify-center">
              <MapPin className="w-8 h-8 text-primary" />
            </div>
            <div className="text-2xl font-bold">5+</div>
            <div className="text-sm text-muted-foreground">Years in Halifax</div>
          </div>
          <div className="text-center space-y-2">
            <div className="flex justify-center">
              <Clock className="w-8 h-8 text-green-600" />
            </div>
            <div className="text-2xl font-bold">Weekly</div>
            <div className="text-sm text-muted-foreground">New Reviews</div>
          </div>
          <div className="text-center space-y-2">
            <div className="flex justify-center">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <div className="text-2xl font-bold">50K+</div>
            <div className="text-sm text-muted-foreground">Monthly Readers</div>
          </div>
        </section>

        {/* Team */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold">Meet the Team</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                <Users className="w-16 h-16 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">Halifax Food Critic</h3>
                <p className="text-muted-foreground">Lead Reviewer & Founder</p>
                <p className="text-sm">
                  A Halifax native with 15+ years in the culinary industry, including stints at some of the city&apos;s
                  most respected restaurants. Holds a culinary arts degree and has been writing about food for over a decade.
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                <Users className="w-16 h-16 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">Maritime Dining Expert</h3>
                <p className="text-muted-foreground">Contributing Writer</p>
                <p className="text-sm">
                  Specializes in Maritime cuisine and local sourcing. Former chef turned food writer with deep connections 
                  to Halifax&apos;s farming and fishing communities. Brings insider knowledge of seasonal ingredients and traditional techniques.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold">Our Values</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Authenticity</h3>
              <p className="text-sm text-muted-foreground">
                Every review is based on genuine dining experiences. We pay for our meals and never accept compensation 
                that could influence our opinions.
              </p>
            </div>
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Local Focus</h3>
              <p className="text-sm text-muted-foreground">
                We celebrate Halifax&apos;s unique culinary identity while supporting local restaurants, farmers, and food producers
                who make our city&apos;s dining scene special.
              </p>
            </div>
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Constructive Criticism</h3>
              <p className="text-sm text-muted-foreground">
                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback 
                that can help establishments improve.
              </p>
            </div>
          </div>
        </section>

        {/* Contact */}
        <section className="bg-muted/50 rounded-lg p-8 text-center space-y-4">
          <h2 className="text-2xl font-bold">Get in Touch</h2>
          <p className="text-muted-foreground">
            Have a restaurant recommendation or want to collaborate? We&apos;d love to hear from you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href={`mailto:${siteConfig.author.email}`}
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
            >
              Email Us
            </a>
            <a
              href={`https://instagram.com/${siteConfig.social.instagram.replace('@', '')}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
            >
              Follow on Instagram
            </a>
          </div>
        </section>
      </div>
    </div>
  )
}
