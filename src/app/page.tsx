import { getAllReviews, getAverageRating } from '@/lib/content'
import { ReviewCard } from '@/components/ReviewCard'
import { MapPin, Star, Clock } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  const allReviews = getAllReviews()
  const featuredReviews = allReviews.slice(0, 6)
  const averageRating = getAverageRating()
  const totalReviews = allReviews.length

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="container mx-auto px-4 text-center space-y-8">
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              Halifax&apos;s Premier
              <span className="text-primary block">Dining Guide</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Authentic restaurant reviews and culinary experiences across Nova Scotia&apos;s vibrant capital city.
              Discover your next favorite meal with our local expertise.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-400 fill-current" />
              <span className="font-medium">{averageRating}/5 Average Rating</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-primary" />
              <span className="font-medium">{totalReviews}+ Halifax Restaurants</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-green-600" />
              <span className="font-medium">Updated Weekly</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/reviews/"
              className="inline-flex items-center justify-center rounded-md bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
            >
              Browse All Reviews
            </Link>
            <Link
              href="/guides/"
              className="inline-flex items-center justify-center rounded-md border border-input bg-background px-8 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
            >
              Dining Guides
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Reviews */}
      <section className="container mx-auto px-4 space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">Latest Reviews</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Fresh takes on Halifax&apos;s dining scene, from hidden gems to established favorites.
            Each review is based on multiple visits and honest experiences.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredReviews.map((review, index) => (
            <ReviewCard
              key={review.slug}
              review={review}
              priority={index < 3}
            />
          ))}
        </div>

        <div className="text-center">
          <Link
            href="/reviews/"
            className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
          >
            View All Reviews
          </Link>
        </div>
      </section>

      {/* Quick Take Section */}
      <section className="bg-muted/50 py-16">
        <div className="container mx-auto px-4 text-center space-y-8">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold">Why Trust HFX Eats?</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              <strong>Quick Take:</strong> We&apos;re Halifax locals with deep culinary knowledge and years of restaurant experience.
              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">100%</div>
              <div className="text-sm font-medium">Authentic Reviews</div>
              <div className="text-xs text-muted-foreground">No sponsored content or fake reviews</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">5+</div>
              <div className="text-sm font-medium">Years Experience</div>
              <div className="text-xs text-muted-foreground">Deep knowledge of Halifax dining</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">Weekly</div>
              <div className="text-sm font-medium">Fresh Content</div>
              <div className="text-xs text-muted-foreground">Regular updates and new discoveries</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
