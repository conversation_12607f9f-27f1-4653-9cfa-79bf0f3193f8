# HFX Eats - Deployment Guide

## 🚀 Quick Deployment to Vercel

### 1. Prerequisites
- GitHub account
- Vercel account (free tier available)
- Domain name (optional)

### 2. Deploy Steps

1. **Push to GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: Halifax restaurant review blog"
   git branch -M main
   git remote add origin https://github.com/yourusername/hfxeats.git
   git push -u origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Vercel will auto-detect Next.js settings

3. **Environment Variables**
   Add these in Vercel dashboard:
   ```
   NEXT_PUBLIC_SITE_URL=https://yourdomain.com
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
   REVALIDATION_SECRET=your_secret_key_here
   ```

4. **Deploy**
   - Click "Deploy"
   - Your site will be live in ~2 minutes

### 3. Custom Domain (Optional)
1. In Vercel dashboard, go to your project
2. Click "Domains" tab
3. Add your custom domain
4. Update DNS records as instructed

## 📊 Performance Optimization

### Core Web Vitals Targets
- **LCP**: ≤ 2.5s ✅
- **INP**: ≤ 200ms ✅  
- **CLS**: ≤ 0.1 ✅

### Built-in Optimizations
- ✅ Image optimization with Next.js Image
- ✅ Font optimization with `font-display: swap`
- ✅ Code splitting and lazy loading
- ✅ Static generation for fast loading
- ✅ Tailwind CSS purging
- ✅ Gzip compression

## 🔍 SEO Features

### Implemented
- ✅ Structured data (JSON-LD)
- ✅ OpenGraph & Twitter cards
- ✅ Sitemap generation
- ✅ Robots.txt
- ✅ Meta tags optimization
- ✅ Local business schema
- ✅ Breadcrumb navigation
- ✅ Semantic HTML structure

### SEO Checklist for Content
- [ ] Restaurant name in title
- [ ] 40-60 word summary
- [ ] Complete address
- [ ] Optimized images with alt text
- [ ] Internal linking
- [ ] First-hand content
- [ ] Local keywords

## 🛠️ Maintenance

### Adding New Reviews
1. Create `.mdx` file in `content/reviews/`
2. Add front matter with all required fields
3. Write review content
4. Add images to `public/images/[slug]/`
5. Commit and push to trigger deployment

### Updating Content
- Edit existing `.mdx` files
- Use ISR revalidation API for immediate updates
- Monitor Core Web Vitals in Vercel Analytics

### Monitoring
- Vercel Analytics (built-in)
- Google Search Console
- Google Analytics (add to layout.tsx)
- Lighthouse CI (configured)

## 🔧 Advanced Configuration

### GitHub Actions
- Automated testing on PRs
- Lighthouse CI performance checks
- Automatic sitemap updates
- Production deployments

### ISR (Incremental Static Regeneration)
```bash
# Revalidate specific page
curl -X POST "https://yourdomain.com/api/revalidate?secret=YOUR_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"path": "/reviews/restaurant-name"}'
```

### Analytics Integration
Add to `src/app/layout.tsx`:
```tsx
// Google Analytics
<Script src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID" />
<Script id="google-analytics">
  {`
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID');
  `}
</Script>
```

## 📱 PWA Features

The site includes a web manifest for PWA capabilities:
- Add to home screen
- Offline-ready (with service worker)
- App-like experience

## 🔒 Security

### Headers (Configured in vercel.json)
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

### Best Practices
- No inline scripts
- CSP headers (add if needed)
- HTTPS only
- Secure cookies

## 📞 Support

### Common Issues
1. **Build fails**: Check apostrophes in JSX content
2. **Images not loading**: Verify file paths and extensions
3. **SEO not working**: Check meta tags and structured data
4. **Performance issues**: Run Lighthouse audit

### Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Vercel Documentation](https://vercel.com/docs)
- [MDX Documentation](https://mdxjs.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

---

**Your Halifax restaurant review blog is ready to launch! 🎉**
