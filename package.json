{"name": "hfxeats", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "postbuild": "next-sitemap"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.3", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@types/mdx": "^2.0.13", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "gray-matter": "^4.0.3", "lucide-react": "^0.525.0", "next": "15.4.3", "next-mdx-remote": "^5.0.0", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "react": "19.1.0", "react-dom": "19.1.0", "reading-time": "^1.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}