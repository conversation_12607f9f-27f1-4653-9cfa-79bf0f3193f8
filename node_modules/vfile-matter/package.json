{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/vfile/vfile-matter/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"vfile": "^6.0.0", "yaml": "^2.0.0"}, "description": "vfile utility to parse the YAML front matter in a file", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": "./index.js", "files": ["index.d.ts.map", "index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["file", "frontmatter", "matter", "utility", "util", "vfile-util", "vfile", "virtual", "yaml"], "license": "MIT", "name": "vfile-matter", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "vfile/vfile-matter", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "5.0.1", "xo": {"prettier": true}}