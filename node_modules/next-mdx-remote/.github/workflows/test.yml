name: 'Test'
on:
  pull_request:
  push:
    branches: [main]

jobs:
  test:
    name: 'Run Tests 🧪'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Setup Node
        uses: actions/setup-node@60edb5dd545a775178f52524783378180af0d1f8 # v4.0.2
        with:
          node-version: '18.x'
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
      - name: Install and build
        run: |
          npm ci
      - name: Run Tests
        run: npm run test
