{"name": "unist-util-visit-parents", "version": "5.1.3", "description": "unist utility to recursively walk over nodes, with ancestral information", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "tree", "ast", "visit", "traverse", "walk", "check", "parent", "parents"], "repository": "syntax-tree/unist-util-visit-parents", "bugs": "https://github.com/syntax-tree/unist-util-visit-parents/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "browser": {"./lib/color.js": "./lib/color.browser.js"}, "react-native": {"./lib/color.js": "./lib/color.browser.js"}, "types": "index.d.ts", "files": ["lib/", "complex-types.d.ts", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^2.0.0", "unist-util-is": "^5.0.0"}, "devDependencies": {"@types/hast": "^2.0.0", "@types/mdast": "^3.0.0", "@types/node": "^18.0.0", "c8": "^7.0.0", "mdast-util-from-markdown": "^1.0.0", "mdast-util-gfm": "^2.0.0", "micromark-extension-gfm": "^2.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "strip-ansi": "^7.0.0", "tsd": "^0.25.0", "type-coverage": "^2.0.0", "typescript": "^4.7.0", "xo": "^0.53.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"@typescript-eslint/array-type": "off"}}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true, "#": "needed `any`s", "ignoreFiles": ["lib/complex-types.d.ts"]}}