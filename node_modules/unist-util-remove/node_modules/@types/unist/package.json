{"name": "@types/unist", "version": "2.0.11", "description": "TypeScript definitions for unist", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/unist", "license": "MIT", "contributors": [{"name": "bizen241", "githubUsername": "bizen241", "url": "https://github.com/bizen241"}, {"name": "<PERSON>", "githubUsername": "lujun2", "url": "https://github.com/lujun2"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/hrajchert"}, {"name": "<PERSON>", "githubUsername": "wooorm", "url": "https://github.com/wooorm"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "rokt33r", "url": "https://github.com/rokt33r"}, {"name": "<PERSON>", "githubUsername": "GuiltyDolphin", "url": "https://github.com/GuiltyDolphin"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/unist"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "6e36525a6db49ae5517fe0751796ca8f6c65099098415046d4f1ad6c2ef1a33c", "typeScriptVersion": "4.8"}