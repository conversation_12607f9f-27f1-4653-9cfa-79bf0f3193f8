# HFX Eats - Halifax Restaurant Review Blog

A modern, SEO-optimized restaurant review blog built with Next.js 14, focusing on Halifax, Nova Scotia's dining scene. Features static site generation, MDX content management, and comprehensive SEO optimization for 2025.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

Open [http://localhost:3000](http://localhost:3000) to view the site.

## 🏗️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **Content**: MDX with gray-matter
- **SEO**: next-seo + structured data
- **Images**: Next.js Image optimization
- **Deployment**: Vercel-ready

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── reviews/[slug]/     # Dynamic review pages
│   │   └── layout.tsx          # Root layout with SEO
│   ├── components/             # Reusable UI components
│   │   ├── RatingStars.tsx     # Star rating component
│   │   ├── ProsCons.tsx        # Pros/cons lists
│   │   ├── MapEmbed.tsx        # Google Maps integration
│   │   └── ReviewCard.tsx      # Review preview cards
│   └── lib/                    # Utilities and helpers
│       ├── seo.ts              # SEO utilities & structured data
│       ├── content.ts          # MDX content processing
│       └── utils.ts            # General utilities
├── content/
│   └── reviews/                # MDX review files
├── public/
│   └── images/                 # Optimized images
└── README.md
```

## ✍️ Creating Content

### Adding a New Review

1. Create a new `.mdx` file in `content/reviews/`:

```bash
touch content/reviews/restaurant-name-halifax.mdx
```

2. Add front matter and content:

```yaml
---
title: "Restaurant Name Review – Honest Take on Halifax Dining"
summary: "A 40-60 word summary that works well for AI Overviews and social sharing."
rating: 4.2
address: "123 Main St, Halifax, NS"
lat: 44.6488
lng: -63.5752
priceLevel: "$$"
cuisine: "Italian, Contemporary"
datePublished: "2025-01-24"
lastUpdated: "2025-01-24"
image: "restaurant-hero.jpg"
author: "Your Name"
---

Your review content here...
```

### Front Matter Fields

| Field | Type | Description |
|-------|------|-------------|
| `title` | string | SEO-optimized title (include restaurant name + "Review") |
| `summary` | string | 40-60 words for AI Overviews and social sharing |
| `rating` | number | 1-5 star rating (decimals allowed) |
| `address` | string | Full street address |
| `lat` | number | Latitude for map embedding |
| `lng` | number | Longitude for map embedding |
| `priceLevel` | string | $, $$, $$$, or $$$$ |
| `cuisine` | string | Comma-separated cuisine types |
| `datePublished` | string | YYYY-MM-DD format |
| `lastUpdated` | string | YYYY-MM-DD format |
| `image` | string | Filename in `/public/images/[slug]/` |
| `author` | string | Author name (optional) |

### Using MDX Components

```mdx
<!-- Pros and cons list -->
<ProsCons
  pros={["Great atmosphere", "Fresh ingredients"]}
  cons={["Expensive", "Long wait times"]}
/>

<!-- Interactive map -->
<MapEmbed lat={44.6488} lng={-63.5752} address="123 Main St, Halifax, NS" />

<!-- Rating stars -->
<RatingStars rating={4.5} size="lg" />
```

## 🎨 Styling & Components

Built with Tailwind CSS and shadcn/ui for consistent, accessible design:

- **Dark mode ready** - Automatic system preference detection
- **Mobile-first** - Responsive design for all screen sizes
- **Accessible** - WCAG compliant components
- **Fast loading** - Optimized images and fonts

### Adding New Components

```bash
# Add shadcn/ui components
npx shadcn@latest add button
npx shadcn@latest add card
```

## 🔍 SEO Features

### Built-in SEO Optimization

- **Structured Data**: Automatic JSON-LD for reviews, restaurants, and local business
- **Meta Tags**: Dynamic OpenGraph and Twitter cards
- **Sitemaps**: Auto-generated with next-sitemap
- **Core Web Vitals**: Optimized for LCP, INP, and CLS
- **Local SEO**: Halifax-focused schema and content

### SEO Checklist for New Reviews

- [ ] Title includes restaurant name + "Review"
- [ ] Summary is 40-60 words (AI Overview optimized)
- [ ] Address is complete and accurate
- [ ] Images are optimized (WebP preferred)
- [ ] Alt text is descriptive, not keyword-stuffed
- [ ] Internal links to related reviews
- [ ] Unique, first-hand content

## 🗺️ Maps Integration

To enable interactive maps:

1. Get a Google Maps API key
2. Add to `.env.local`:

```bash
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

Without an API key, maps will show a fallback with a link to Google Maps.

## 📊 Analytics & Performance

### Core Web Vitals Targets

- **LCP**: ≤ 2.5s
- **INP**: ≤ 200ms
- **CLS**: ≤ 0.1

### Performance Features

- Image optimization with Next.js Image
- Font optimization with `font-display: swap`
- Code splitting and lazy loading
- Static generation for fast loading

## 🚀 Deployment

### Vercel (Recommended)

1. Push to GitHub
2. Connect to Vercel
3. Deploy automatically

### Environment Variables

```bash
# .env.local
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

### Build Commands

```bash
# Development
npm run dev

# Production build
npm run build
npm start

# Lint and type check
npm run lint
npm run type-check
```

## 📝 Content Guidelines

### Writing Style

- **Lead with experience**: "I visited on [date] after..."
- **Keep it punchy**: 6-8th grade reading level
- **Use local keywords**: "Halifax brunch spots", "South End pizza"
- **Include original photos**: Alt text should be descriptive
- **Add a "Verdict" section**: Boosts Featured Snippet chances

### Review Structure

1. **Quick Take** (40-60 words)
2. **Atmosphere & Service**
3. **Menu Highlights** (with ratings)
4. **Value Assessment**
5. **Final Verdict** with rating

### E-E-A-T Signals

- Author bio with culinary background
- Halifax residency mentioned
- First-hand tasting notes
- No AI-generated content
- Unique photography

## 🔧 Development

### Adding New Features

1. Create components in `src/components/`
2. Add utilities to `src/lib/`
3. Update types in TypeScript files
4. Test with sample content

### Content Management

- Reviews are stored as MDX files
- Front matter parsed with gray-matter
- Content processed at build time
- Static generation for performance

## 📞 Support

For questions about setup or customization:

1. Check the documentation
2. Review sample content
3. Test with development server
4. Deploy to staging first

---

**Made with ❤️ in Halifax, Nova Scotia**
