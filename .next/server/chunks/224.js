exports.id=224,exports.ids=[224],exports.modules={14350:(a,b,c)=>{"use strict";c.d(b,{CQ:()=>d,UB:()=>g,X2:()=>e,qe:()=>h,tk:()=>f});let d={name:"HFX Eats",description:"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.",url:"https://hfxeats.com",ogImage:"https://hfxeats.com/og-image.jpg",author:{name:"Halifax Food Critic",url:"https://hfxeats.com/about",email:"<EMAIL>"},social:{twitter:"@hfxeats",instagram:"@hfxeats",facebook:"hfxeats"}};function e(a,b="review"){let{title:c,summary:f,slug:g,image:h}=a,i=`${d.url}/${"review"===b?"reviews":""}/${g}/`,j=h?`${d.url}/images/${g}/${h}`:d.ogImage;return{title:`${c} | ${d.name}`,description:f,authors:[{name:d.author.name,url:d.author.url}],creator:d.author.name,publisher:d.name,formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(d.url),alternates:{canonical:i},openGraph:{title:c,description:f,url:i,siteName:d.name,images:[{url:j,width:1200,height:630,alt:c}],locale:"en_CA",type:"article"},twitter:{card:"summary_large_image",title:c,description:f,images:[j],creator:d.social.twitter},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}}}function f(a){let{title:b,summary:c,rating:e,address:f,datePublished:g,lastUpdated:h,slug:i,author:j=d.author.name}=a;return{"@context":"https://schema.org","@graph":[{"@type":"Review","@id":`${d.url}/reviews/${i}/#review`,headline:b,description:c,datePublished:g,dateModified:h,author:{"@type":"Person",name:j,url:d.author.url},publisher:{"@type":"Organization",name:d.name,url:d.url},reviewRating:{"@type":"Rating",ratingValue:e,bestRating:5,worstRating:1},itemReviewed:{"@type":"Restaurant",name:b.replace(" Review","").replace(" – Worth the Hype in 2025?",""),address:{"@type":"PostalAddress",streetAddress:f,addressLocality:"Halifax",addressRegion:"NS",addressCountry:"CA"},servesCuisine:a.cuisine,priceRange:a.priceLevel}},{"@type":"BlogPosting","@id":`${d.url}/reviews/${i}/#blogpost`,headline:b,description:c,datePublished:g,dateModified:h,author:{"@type":"Person",name:j,url:d.author.url},publisher:{"@type":"Organization",name:d.name,url:d.url,logo:{"@type":"ImageObject",url:`${d.url}/logo.png`}},mainEntityOfPage:{"@type":"WebPage","@id":`${d.url}/reviews/${i}/`}}]}}function g(){return{"@context":"https://schema.org","@type":"LocalBusiness","@id":`${d.url}/#localbusiness`,name:d.name,description:d.description,url:d.url,address:{"@type":"PostalAddress",addressLocality:"Halifax",addressRegion:"NS",addressCountry:"CA"},areaServed:{"@type":"City",name:"Halifax",addressRegion:"NS",addressCountry:"CA"},knowsAbout:["Restaurant Reviews","Halifax Dining","Nova Scotia Restaurants","Food Criticism","Culinary Experiences"]}}function h(a){return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:a.map((a,b)=>({"@type":"ListItem",position:b+1,name:a.name,item:a.url}))}}},15888:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},16439:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,metadata:()=>r});var d=c(37413),e=c(7339),f=c.n(e);c(61135);var g=c(14350),h=c(4536),i=c.n(h),j=c(78768),k=c(36287);function l(){return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)(i(),{href:"/",className:"flex items-center space-x-2",children:(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:g.CQ.name})}),(0,d.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,d.jsx)(i(),{href:"/reviews/",className:"text-sm font-medium hover:text-primary transition-colors",children:"Reviews"}),(0,d.jsx)(i(),{href:"/guides/",className:"text-sm font-medium hover:text-primary transition-colors",children:"Guides"}),(0,d.jsx)(i(),{href:"/neighbourhoods/",className:"text-sm font-medium hover:text-primary transition-colors",children:"Neighbourhoods"}),(0,d.jsx)(i(),{href:"/about/",className:"text-sm font-medium hover:text-primary transition-colors",children:"About"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{className:"p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Search",children:(0,d.jsx)(j.A,{className:"h-4 w-4"})}),(0,d.jsx)("button",{className:"md:hidden p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Menu",children:(0,d.jsx)(k.A,{className:"h-4 w-4"})})]})]})})})}var m=c(63353),n=c(56378),o=c(45868),p=c(60343);function q(){let a=new Date().getFullYear();return(0,d.jsx)("footer",{className:"border-t bg-background",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:g.CQ.name}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("a",{href:`https://twitter.com/${g.CQ.social.twitter.replace("@","")}`,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors","aria-label":"Twitter",children:(0,d.jsx)(m.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:`https://instagram.com/${g.CQ.social.instagram.replace("@","")}`,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors","aria-label":"Instagram",children:(0,d.jsx)(n.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:`https://facebook.com/${g.CQ.social.facebook}`,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors","aria-label":"Facebook",children:(0,d.jsx)(o.A,{className:"h-5 w-5"})}),(0,d.jsx)("a",{href:`mailto:${g.CQ.author.email}`,className:"text-muted-foreground hover:text-primary transition-colors","aria-label":"Email",children:(0,d.jsx)(p.A,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold",children:"Reviews"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/reviews/",className:"text-muted-foreground hover:text-primary transition-colors",children:"All Reviews"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/reviews/?rating=5",className:"text-muted-foreground hover:text-primary transition-colors",children:"5-Star Reviews"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/reviews/?cuisine=seafood",className:"text-muted-foreground hover:text-primary transition-colors",children:"Seafood"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/reviews/?price=$$$",className:"text-muted-foreground hover:text-primary transition-colors",children:"Fine Dining"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold",children:"Neighbourhoods"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/neighbourhoods/downtown/",className:"text-muted-foreground hover:text-primary transition-colors",children:"Downtown"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/neighbourhoods/north-end/",className:"text-muted-foreground hover:text-primary transition-colors",children:"North End"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/neighbourhoods/south-end/",className:"text-muted-foreground hover:text-primary transition-colors",children:"South End"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/neighbourhoods/dartmouth/",className:"text-muted-foreground hover:text-primary transition-colors",children:"Dartmouth"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold",children:"About"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/about/",className:"text-muted-foreground hover:text-primary transition-colors",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/editorial-guidelines/",className:"text-muted-foreground hover:text-primary transition-colors",children:"Editorial Guidelines"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/contact/",className:"text-muted-foreground hover:text-primary transition-colors",children:"Contact"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/privacy/",className:"text-muted-foreground hover:text-primary transition-colors",children:"Privacy Policy"})})]})]})]}),(0,d.jsx)("div",{className:"mt-8 pt-8 border-t text-center text-sm text-muted-foreground",children:(0,d.jsxs)("p",{children:["\xa9 ",a," ",g.CQ.name,". All rights reserved. Made with ❤️ in Halifax, Nova Scotia."]})})]})})}let r={title:{default:g.CQ.name,template:`%s | ${g.CQ.name}`},description:g.CQ.description,keywords:["Halifax restaurants","Nova Scotia dining","restaurant reviews","Halifax food","dining guide","HFX eats","Halifax food critic"],authors:[{name:g.CQ.author.name,url:g.CQ.author.url}],creator:g.CQ.author.name,publisher:g.CQ.name,formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(g.CQ.url),openGraph:{type:"website",locale:"en_CA",url:g.CQ.url,title:g.CQ.name,description:g.CQ.description,siteName:g.CQ.name,images:[{url:g.CQ.ogImage,width:1200,height:630,alt:g.CQ.name}]},twitter:{card:"summary_large_image",title:g.CQ.name,description:g.CQ.description,images:[g.CQ.ogImage],creator:g.CQ.social.twitter},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function s({children:a}){let b=(0,g.UB)();return(0,d.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,d.jsx)("head",{children:(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(b)}})}),(0,d.jsx)("body",{className:f().className,children:(0,d.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,d.jsx)(l,{}),(0,d.jsx)("main",{className:"flex-1",children:a}),(0,d.jsx)(q,{})]})})]})}},57744:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},61135:()=>{},76792:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},86520:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))},95968:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]}};