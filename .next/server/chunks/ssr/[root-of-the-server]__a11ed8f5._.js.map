{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/lib/content.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\nimport readingTime from 'reading-time'\nimport { ReviewFrontMatter } from './seo'\n\nconst contentDirectory = path.join(process.cwd(), 'content')\nconst reviewsDirectory = path.join(contentDirectory, 'reviews')\n\nexport interface ReviewPost extends ReviewFrontMatter {\n  slug: string\n  content: string\n  readingTime: {\n    text: string\n    minutes: number\n    time: number\n    words: number\n  }\n}\n\nexport function getAllReviewSlugs(): string[] {\n  if (!fs.existsSync(reviewsDirectory)) {\n    return []\n  }\n  \n  const files = fs.readdirSync(reviewsDirectory)\n  return files\n    .filter((file) => file.endsWith('.mdx'))\n    .map((file) => file.replace(/\\.mdx$/, ''))\n}\n\nexport function getReviewBySlug(slug: string): ReviewPost | null {\n  try {\n    const fullPath = path.join(reviewsDirectory, `${slug}.mdx`)\n    \n    if (!fs.existsSync(fullPath)) {\n      return null\n    }\n    \n    const fileContents = fs.readFileSync(fullPath, 'utf8')\n    const { data, content } = matter(fileContents)\n    const readingTimeResult = readingTime(content)\n\n    return {\n      slug,\n      content,\n      readingTime: readingTimeResult,\n      title: data.title || '',\n      summary: data.summary || '',\n      rating: data.rating || 0,\n      address: data.address || '',\n      lat: data.lat || 0,\n      lng: data.lng || 0,\n      priceLevel: data.priceLevel || '',\n      cuisine: data.cuisine || '',\n      datePublished: data.datePublished || '',\n      lastUpdated: data.lastUpdated || data.datePublished || '',\n      image: data.image || '',\n      author: data.author || '',\n    }\n  } catch (error) {\n    console.error(`Error reading review ${slug}:`, error)\n    return null\n  }\n}\n\nexport function getAllReviews(): ReviewPost[] {\n  const slugs = getAllReviewSlugs()\n  const reviews = slugs\n    .map((slug) => getReviewBySlug(slug))\n    .filter((review): review is ReviewPost => review !== null)\n    .sort((a, b) => {\n      // Sort by date published, newest first\n      return new Date(b.datePublished).getTime() - new Date(a.datePublished).getTime()\n    })\n\n  return reviews\n}\n\nexport function getReviewsByRating(minRating: number = 4): ReviewPost[] {\n  return getAllReviews().filter((review) => review.rating >= minRating)\n}\n\nexport function getReviewsByCuisine(cuisine: string): ReviewPost[] {\n  return getAllReviews().filter((review) => \n    review.cuisine.toLowerCase().includes(cuisine.toLowerCase())\n  )\n}\n\nexport function getReviewsByPriceLevel(priceLevel: string): ReviewPost[] {\n  return getAllReviews().filter((review) => review.priceLevel === priceLevel)\n}\n\nexport function getRelatedReviews(currentSlug: string, limit: number = 3): ReviewPost[] {\n  const currentReview = getReviewBySlug(currentSlug)\n  if (!currentReview) return []\n\n  const allReviews = getAllReviews().filter((review) => review.slug !== currentSlug)\n  \n  // Score reviews based on similarity\n  const scoredReviews = allReviews.map((review) => {\n    let score = 0\n    \n    // Same cuisine gets high score\n    if (review.cuisine === currentReview.cuisine) score += 3\n    \n    // Similar price level gets medium score\n    if (review.priceLevel === currentReview.priceLevel) score += 2\n    \n    // Similar rating gets low score\n    if (Math.abs(review.rating - currentReview.rating) <= 0.5) score += 1\n    \n    return { review, score }\n  })\n  \n  // Sort by score and return top results\n  return scoredReviews\n    .sort((a, b) => b.score - a.score)\n    .slice(0, limit)\n    .map((item) => item.review)\n}\n\nexport function searchReviews(query: string): ReviewPost[] {\n  const allReviews = getAllReviews()\n  const lowercaseQuery = query.toLowerCase()\n  \n  return allReviews.filter((review) => {\n    return (\n      review.title.toLowerCase().includes(lowercaseQuery) ||\n      review.summary.toLowerCase().includes(lowercaseQuery) ||\n      review.cuisine.toLowerCase().includes(lowercaseQuery) ||\n      review.address.toLowerCase().includes(lowercaseQuery)\n    )\n  })\n}\n\n// Utility function to format date for display\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-CA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\n// Utility function to get average rating\nexport function getAverageRating(): number {\n  const reviews = getAllReviews()\n  if (reviews.length === 0) return 0\n  \n  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)\n  return Math.round((totalRating / reviews.length) * 10) / 10\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAGA,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAClD,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB;AAa9C,SAAS;IACd,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,mBAAmB;QACpC,OAAO,EAAE;IACX;IAEA,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;IAC7B,OAAO,MACJ,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,SAC/B,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO,CAAC,UAAU;AAC1C;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,KAAK,IAAI,CAAC;QAE1D,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,OAAO;QACT;QAEA,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QACjC,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,UAAW,AAAD,EAAE;QAEtC,OAAO;YACL;YACA;YACA,aAAa;YACb,OAAO,KAAK,KAAK,IAAI;YACrB,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,KAAK,MAAM,IAAI;YACvB,SAAS,KAAK,OAAO,IAAI;YACzB,KAAK,KAAK,GAAG,IAAI;YACjB,KAAK,KAAK,GAAG,IAAI;YACjB,YAAY,KAAK,UAAU,IAAI;YAC/B,SAAS,KAAK,OAAO,IAAI;YACzB,eAAe,KAAK,aAAa,IAAI;YACrC,aAAa,KAAK,WAAW,IAAI,KAAK,aAAa,IAAI;YACvD,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC,EAAE;QAC/C,OAAO;IACT;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IACd,MAAM,UAAU,MACb,GAAG,CAAC,CAAC,OAAS,gBAAgB,OAC9B,MAAM,CAAC,CAAC,SAAiC,WAAW,MACpD,IAAI,CAAC,CAAC,GAAG;QACR,uCAAuC;QACvC,OAAO,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO;IAChF;IAEF,OAAO;AACT;AAEO,SAAS,mBAAmB,YAAoB,CAAC;IACtD,OAAO,gBAAgB,MAAM,CAAC,CAAC,SAAW,OAAO,MAAM,IAAI;AAC7D;AAEO,SAAS,oBAAoB,OAAe;IACjD,OAAO,gBAAgB,MAAM,CAAC,CAAC,SAC7B,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;AAE7D;AAEO,SAAS,uBAAuB,UAAkB;IACvD,OAAO,gBAAgB,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,KAAK;AAClE;AAEO,SAAS,kBAAkB,WAAmB,EAAE,QAAgB,CAAC;IACtE,MAAM,gBAAgB,gBAAgB;IACtC,IAAI,CAAC,eAAe,OAAO,EAAE;IAE7B,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,SAAW,OAAO,IAAI,KAAK;IAEtE,oCAAoC;IACpC,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAC;QACpC,IAAI,QAAQ;QAEZ,+BAA+B;QAC/B,IAAI,OAAO,OAAO,KAAK,cAAc,OAAO,EAAE,SAAS;QAEvD,wCAAwC;QACxC,IAAI,OAAO,UAAU,KAAK,cAAc,UAAU,EAAE,SAAS;QAE7D,gCAAgC;QAChC,IAAI,KAAK,GAAG,CAAC,OAAO,MAAM,GAAG,cAAc,MAAM,KAAK,KAAK,SAAS;QAEpE,OAAO;YAAE;YAAQ;QAAM;IACzB;IAEA,uCAAuC;IACvC,OAAO,cACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM;AAC9B;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,MAAM,iBAAiB,MAAM,WAAW;IAExC,OAAO,WAAW,MAAM,CAAC,CAAC;QACxB,OACE,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACpC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE1C;AACF;AAGO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IAEjC,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;IACzE,OAAO,KAAK,KAAK,CAAC,AAAC,cAAc,QAAQ,MAAM,GAAI,MAAM;AAC3D", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/components/RatingStars.tsx"], "sourcesContent": ["import { Star } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface RatingStarsProps {\n  rating: number\n  maxRating?: number\n  size?: 'sm' | 'md' | 'lg'\n  showNumber?: boolean\n  className?: string\n}\n\nexport function RatingStars({ \n  rating, \n  maxRating = 5, \n  size = 'md', \n  showNumber = true,\n  className \n}: RatingStarsProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg'\n  }\n\n  return (\n    <div className={cn('flex items-center gap-2', className)}>\n      <div className=\"flex items-center gap-0.5\">\n        {Array.from({ length: maxRating }, (_, i) => {\n          const starValue = i + 1\n          const isFilled = starValue <= Math.floor(rating)\n          const isHalfFilled = starValue === Math.ceil(rating) && rating % 1 !== 0\n          \n          return (\n            <div key={i} className=\"relative\">\n              <Star\n                className={cn(\n                  sizeClasses[size],\n                  'text-gray-300 dark:text-gray-600'\n                )}\n                fill=\"currentColor\"\n              />\n              {(isFilled || isHalfFilled) && (\n                <Star\n                  className={cn(\n                    sizeClasses[size],\n                    'absolute top-0 left-0 text-yellow-400',\n                    isHalfFilled && 'clip-path-half'\n                  )}\n                  fill=\"currentColor\"\n                  style={\n                    isHalfFilled\n                      ? { clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)' }\n                      : undefined\n                  }\n                />\n              )}\n            </div>\n          )\n        })}\n      </div>\n      {showNumber && (\n        <span className={cn(\n          'font-medium text-gray-900 dark:text-gray-100',\n          textSizeClasses[size]\n        )}>\n          {rating.toFixed(1)}\n        </span>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUO,SAAS,YAAY,EAC1B,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,aAAa,IAAI,EACjB,SAAS,EACQ;IACjB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG;oBACrC,MAAM,YAAY,IAAI;oBACtB,MAAM,WAAW,aAAa,KAAK,KAAK,CAAC;oBACzC,MAAM,eAAe,cAAc,KAAK,IAAI,CAAC,WAAW,SAAS,MAAM;oBAEvE,qBACE,8OAAC;wBAAY,WAAU;;0CACrB,8OAAC,kMAAA,CAAA,OAAI;gCACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB;gCAEF,MAAK;;;;;;4BAEN,CAAC,YAAY,YAAY,mBACxB,8OAAC,kMAAA,CAAA,OAAI;gCACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB,yCACA,gBAAgB;gCAElB,MAAK;gCACL,OACE,eACI;oCAAE,UAAU;gCAAwC,IACpD;;;;;;;uBAnBF;;;;;gBAyBd;;;;;;YAED,4BACC,8OAAC;gBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,gDACA,eAAe,CAAC,KAAK;0BAEpB,OAAO,OAAO,CAAC;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/components/ReviewCard.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport Image from 'next/image'\nimport { Calendar, MapPin, Clock } from 'lucide-react'\nimport { RatingStars } from './RatingStars'\nimport { ReviewPost } from '@/lib/content'\nimport { formatDate } from '@/lib/content'\nimport { cn } from '@/lib/utils'\n\ninterface ReviewCardProps {\n  review: ReviewPost\n  className?: string\n  priority?: boolean\n}\n\nexport function ReviewCard({ review, className, priority = false }: ReviewCardProps) {\n  const imageUrl = review.image \n    ? `/images/${review.slug}/${review.image}`\n    : '/images/placeholder-restaurant.jpg'\n\n  return (\n    <article className={cn(\n      'group bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow duration-200',\n      className\n    )}>\n      <Link href={`/reviews/${review.slug}/`} className=\"block\">\n        <div className=\"aspect-video relative overflow-hidden rounded-t-lg\">\n          <Image\n            src={imageUrl}\n            alt={review.title}\n            fill\n            className=\"object-cover group-hover:scale-105 transition-transform duration-200\"\n            priority={priority}\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n          />\n          <div className=\"absolute top-3 right-3\">\n            <div className=\"bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full px-2 py-1\">\n              <RatingStars rating={review.rating} size=\"sm\" showNumber={false} />\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"p-6 space-y-4\">\n          <div className=\"space-y-2\">\n            <h3 className=\"text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors\">\n              {review.title}\n            </h3>\n            <p className=\"text-muted-foreground line-clamp-3\">\n              {review.summary}\n            </p>\n          </div>\n          \n          <div className=\"flex items-center justify-between text-sm text-muted-foreground\">\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-1\">\n                <MapPin className=\"w-4 h-4\" />\n                <span className=\"truncate max-w-[120px]\">\n                  {review.address.split(',')[0]}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <span className=\"font-medium\">{review.priceLevel}</span>\n              </div>\n            </div>\n            <RatingStars rating={review.rating} size=\"sm\" />\n          </div>\n          \n          <div className=\"flex items-center justify-between text-xs text-muted-foreground pt-2 border-t\">\n            <div className=\"flex items-center gap-1\">\n              <Calendar className=\"w-3 h-3\" />\n              <span>{formatDate(review.datePublished)}</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Clock className=\"w-3 h-3\" />\n              <span>{review.readingTime.text}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex flex-wrap gap-2\">\n            {review.cuisine.split(',').map((cuisine, index) => (\n              <span\n                key={index}\n                className=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground\"\n              >\n                {cuisine.trim()}\n              </span>\n            ))}\n          </div>\n        </div>\n      </Link>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;;;;;;;;AAQO,SAAS,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,KAAK,EAAmB;IACjF,MAAM,WAAW,OAAO,KAAK,GACzB,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,GACxC;IAEJ,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,4FACA;kBAEA,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;YAAE,WAAU;;8BAChD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK,OAAO,KAAK;4BACjB,IAAI;4BACJ,WAAU;4BACV,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,cAAW;oCAAC,QAAQ,OAAO,MAAM;oCAAE,MAAK;oCAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;8BAKhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;8CAEf,8OAAC;oCAAE,WAAU;8CACV,OAAO,OAAO;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DACb,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAe,OAAO,UAAU;;;;;;;;;;;;;;;;;8CAGpD,8OAAC,iIAAA,CAAA,cAAW;oCAAC,QAAQ,OAAO,MAAM;oCAAE,MAAK;;;;;;;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,OAAO,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;oCAEC,WAAU;8CAET,QAAQ,IAAI;mCAHR;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrB", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { getAllReviews, getAverageRating } from '@/lib/content'\nimport { ReviewCard } from '@/components/ReviewCard'\nimport { RatingStars } from '@/components/RatingStars'\n\nexport const metadata: Metadata = {\n  title: 'Restaurant Reviews',\n  description: 'Browse all our Halifax restaurant reviews. Discover the best dining experiences across Nova Scotia&apos;s capital city with honest, detailed reviews.',\n  openGraph: {\n    title: 'Halifax Restaurant Reviews | HFX Eats',\n    description: 'Browse all our Halifax restaurant reviews. Discover the best dining experiences across Nova Scotia&apos;s capital city.',\n  },\n}\n\nexport default function ReviewsPage() {\n  const allReviews = getAllReviews()\n  const averageRating = getAverageRating()\n  const totalReviews = allReviews.length\n\n  return (\n    <div className=\"container mx-auto px-4 py-12 space-y-12\">\n      {/* Header */}\n      <div className=\"text-center space-y-6\">\n        <h1 className=\"text-4xl md:text-5xl font-bold\">\n          Halifax Restaurant Reviews\n        </h1>\n        <p className=\"text-xl text-muted-foreground max-w-3xl mx-auto\">\n          Honest, detailed reviews of Halifax&apos;s dining scene. From hidden gems to established favorites,\n          discover your next great meal with our local expertise.\n        </p>\n        \n        <div className=\"flex flex-col sm:flex-row items-center justify-center gap-8 text-sm\">\n          <div className=\"flex items-center gap-2\">\n            <RatingStars rating={averageRating} size=\"sm\" showNumber={false} />\n            <span className=\"font-medium\">{averageRating}/5 Average Rating</span>\n          </div>\n          <div className=\"font-medium\">\n            {totalReviews} Restaurant{totalReviews !== 1 ? 's' : ''} Reviewed\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap gap-4 justify-center\">\n        <button className=\"px-4 py-2 rounded-full bg-primary text-primary-foreground text-sm font-medium\">\n          All Reviews\n        </button>\n        <button className=\"px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent\">\n          5 Stars\n        </button>\n        <button className=\"px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent\">\n          Seafood\n        </button>\n        <button className=\"px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent\">\n          Fine Dining\n        </button>\n        <button className=\"px-4 py-2 rounded-full border border-input bg-background text-sm font-medium hover:bg-accent\">\n          Casual\n        </button>\n      </div>\n\n      {/* Reviews Grid */}\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {allReviews.map((review, index) => (\n          <ReviewCard \n            key={review.slug} \n            review={review}\n            priority={index < 6}\n          />\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {allReviews.length === 0 && (\n        <div className=\"text-center py-16 space-y-4\">\n          <h2 className=\"text-2xl font-semibold\">No Reviews Yet</h2>\n          <p className=\"text-muted-foreground\">\n            We&apos;re working on bringing you the best Halifax restaurant reviews. Check back soon!\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,gBAAgB,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,eAAe,WAAW,MAAM;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAG/C,8OAAC;wBAAE,WAAU;kCAAkD;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,cAAW;wCAAC,QAAQ;wCAAe,MAAK;wCAAK,YAAY;;;;;;kDAC1D,8OAAC;wCAAK,WAAU;;4CAAe;4CAAc;;;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;oCACZ;oCAAa;oCAAY,iBAAiB,IAAI,MAAM;oCAAG;;;;;;;;;;;;;;;;;;;0BAM9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCAAgF;;;;;;kCAGlG,8OAAC;wBAAO,WAAU;kCAA+F;;;;;;kCAGjH,8OAAC;wBAAO,WAAU;kCAA+F;;;;;;kCAGjH,8OAAC;wBAAO,WAAU;kCAA+F;;;;;;kCAGjH,8OAAC;wBAAO,WAAU;kCAA+F;;;;;;;;;;;;0BAMnH,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,8OAAC,gIAAA,CAAA,aAAU;wBAET,QAAQ;wBACR,UAAU,QAAQ;uBAFb,OAAO,IAAI;;;;;;;;;;YAQrB,WAAW,MAAM,KAAK,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}