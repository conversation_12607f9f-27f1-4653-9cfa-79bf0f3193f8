{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hfxeats/src/app/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { siteConfig } from '@/lib/seo'\nimport { MapPin, Clock, Star, Users } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'About Us',\n  description: 'Learn about HFX Eats, Halifax&apos;s premier restaurant review blog. Meet our team of local food critics and discover our commitment to authentic dining experiences.',\n}\n\nexport default function AboutPage() {\n  return (\n    <div className=\"container mx-auto px-4 py-12 max-w-4xl\">\n      <div className=\"space-y-16\">\n        {/* Header */}\n        <div className=\"text-center space-y-6\">\n          <h1 className=\"text-4xl md:text-5xl font-bold\">\n            About HFX Eats\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Halifax&apos;s most trusted restaurant review blog, dedicated to uncovering the best dining experiences\n            across Nova Scotia&apos;s vibrant capital city.\n          </p>\n        </div>\n\n        {/* Mission */}\n        <section className=\"space-y-6\">\n          <h2 className=\"text-3xl font-bold\">Our Mission</h2>\n          <div className=\"prose prose-lg max-w-none dark:prose-invert\">\n            <p>\n              <strong>Quick Take:</strong> We&apos;re Halifax locals with deep culinary knowledge and years of restaurant experience.\n              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.\n            </p>\n            <p>\n              Founded in 2020, HFX Eats emerged from a simple belief: Halifax deserves thoughtful, honest restaurant criticism \n              that celebrates our unique Maritime culinary identity while holding establishments to high standards. We&apos;re not just reviewers –\n              we&apos;re passionate advocates for Halifax&apos;s incredible food scene.\n            </p>\n          </div>\n        </section>\n\n        {/* Stats */}\n        <section className=\"grid md:grid-cols-4 gap-8 py-8 border-y\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"flex justify-center\">\n              <Star className=\"w-8 h-8 text-yellow-400 fill-current\" />\n            </div>\n            <div className=\"text-2xl font-bold\">200+</div>\n            <div className=\"text-sm text-muted-foreground\">Restaurants Reviewed</div>\n          </div>\n          <div className=\"text-center space-y-2\">\n            <div className=\"flex justify-center\">\n              <MapPin className=\"w-8 h-8 text-primary\" />\n            </div>\n            <div className=\"text-2xl font-bold\">5+</div>\n            <div className=\"text-sm text-muted-foreground\">Years in Halifax</div>\n          </div>\n          <div className=\"text-center space-y-2\">\n            <div className=\"flex justify-center\">\n              <Clock className=\"w-8 h-8 text-green-600\" />\n            </div>\n            <div className=\"text-2xl font-bold\">Weekly</div>\n            <div className=\"text-sm text-muted-foreground\">New Reviews</div>\n          </div>\n          <div className=\"text-center space-y-2\">\n            <div className=\"flex justify-center\">\n              <Users className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <div className=\"text-2xl font-bold\">50K+</div>\n            <div className=\"text-sm text-muted-foreground\">Monthly Readers</div>\n          </div>\n        </section>\n\n        {/* Team */}\n        <section className=\"space-y-8\">\n          <h2 className=\"text-3xl font-bold\">Meet the Team</h2>\n          \n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"space-y-4\">\n              <div className=\"aspect-square bg-muted rounded-lg flex items-center justify-center\">\n                <Users className=\"w-16 h-16 text-muted-foreground\" />\n              </div>\n              <div className=\"space-y-2\">\n                <h3 className=\"text-xl font-semibold\">Halifax Food Critic</h3>\n                <p className=\"text-muted-foreground\">Lead Reviewer & Founder</p>\n                <p className=\"text-sm\">\n                  A Halifax native with 15+ years in the culinary industry, including stints at some of the city&apos;s\n                  most respected restaurants. Holds a culinary arts degree and has been writing about food for over a decade.\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"aspect-square bg-muted rounded-lg flex items-center justify-center\">\n                <Users className=\"w-16 h-16 text-muted-foreground\" />\n              </div>\n              <div className=\"space-y-2\">\n                <h3 className=\"text-xl font-semibold\">Maritime Dining Expert</h3>\n                <p className=\"text-muted-foreground\">Contributing Writer</p>\n                <p className=\"text-sm\">\n                  Specializes in Maritime cuisine and local sourcing. Former chef turned food writer with deep connections \n                  to Halifax&apos;s farming and fishing communities. Brings insider knowledge of seasonal ingredients and traditional techniques.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Values */}\n        <section className=\"space-y-6\">\n          <h2 className=\"text-3xl font-bold\">Our Values</h2>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            <div className=\"space-y-3\">\n              <h3 className=\"text-lg font-semibold\">Authenticity</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Every review is based on genuine dining experiences. We pay for our meals and never accept compensation \n                that could influence our opinions.\n              </p>\n            </div>\n            <div className=\"space-y-3\">\n              <h3 className=\"text-lg font-semibold\">Local Focus</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                We celebrate Halifax&apos;s unique culinary identity while supporting local restaurants, farmers, and food producers\n                who make our city&apos;s dining scene special.\n              </p>\n            </div>\n            <div className=\"space-y-3\">\n              <h3 className=\"text-lg font-semibold\">Constructive Criticism</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback \n                that can help establishments improve.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact */}\n        <section className=\"bg-muted/50 rounded-lg p-8 text-center space-y-4\">\n          <h2 className=\"text-2xl font-bold\">Get in Touch</h2>\n          <p className=\"text-muted-foreground\">\n            Have a restaurant recommendation or want to collaborate? We&apos;d love to hear from you.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href={`mailto:${siteConfig.author.email}`}\n              className=\"inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90\"\n            >\n              Email Us\n            </a>\n            <a\n              href={`https://instagram.com/${siteConfig.social.instagram.replace('@', '')}`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground\"\n            >\n              Follow on Instagram\n            </a>\n          </div>\n        </section>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiC;;;;;;sCAG/C,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAOjE,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CAG9B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BASP,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;sCAEjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;sCAEjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;sCAEjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAKnD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAO3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU/B,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BASnD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAM,CAAC,OAAO,EAAE,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,EAAE;oCACzC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAM,CAAC,sBAAsB,EAAE,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,KAAK;oCAC7E,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}