1:"$Sreact.fragment"
2:I[6874,["874","static/chunks/874-437a265a67d6cfee.js","63","static/chunks/63-5d41f10033d3d1e1.js","974","static/chunks/app/page-13da5b9205cc0e68.js"],""]
3:I[7555,[],""]
4:I[1295,[],""]
b:I[8393,[],""]
:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/96f6630407e158fb.css","style"]
0:{"P":null,"b":"qyUfQkHXKXNI1T-yC0ITn","p":"","c":["","about",""],"i":false,"f":[[["",{"children":["about",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/96f6630407e158fb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"LocalBusiness\",\"@id\":\"https://hfxeats.com/#localbusiness\",\"name\":\"HFX Eats\",\"description\":\"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\",\"url\":\"https://hfxeats.com\",\"address\":{\"@type\":\"PostalAddress\",\"addressLocality\":\"Halifax\",\"addressRegion\":\"NS\",\"addressCountry\":\"CA\"},\"areaServed\":{\"@type\":\"City\",\"name\":\"Halifax\",\"addressRegion\":\"NS\",\"addressCountry\":\"CA\"},\"knowsAbout\":[\"Restaurant Reviews\",\"Halifax Dining\",\"Nova Scotia Restaurants\",\"Food Criticism\",\"Culinary Experiences\"]}"}}]}],["$","body",null,{"className":"__className_e8ce0c","children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","header",null,{"className":"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"flex h-16 items-center justify-between","children":[["$","$L2",null,{"href":"/","className":"flex items-center space-x-2","children":["$","div",null,{"className":"text-2xl font-bold text-primary","children":"HFX Eats"}]}],["$","nav",null,{"className":"hidden md:flex items-center space-x-8","children":[["$","$L2",null,{"href":"/reviews/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Reviews"}],["$","$L2",null,{"href":"/guides/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Guides"}],["$","$L2",null,{"href":"/neighbourhoods/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Neighbourhoods"}],["$","$L2",null,{"href":"/about/","className":"text-sm font-medium hover:text-primary transition-colors","children":"About"}]]}],["$","div",null,{"className":"flex items-center space-x-4","children":[["$","button",null,{"className":"p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Search","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-search h-4 w-4","aria-hidden":"true","children":[["$","path","14j7rj",{"d":"m21 21-4.34-4.34"}],["$","circle","4ej97u",{"cx":"11","cy":"11","r":"8"}],"$undefined"]}]}],["$","button",null,{"className":"md:hidden p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Menu","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-menu h-4 w-4","aria-hidden":"true","children":[["$","path","1lakjw",{"d":"M4 12h16"}],["$","path","19g7jn",{"d":"M4 18h16"}],["$","path","1o0s65",{"d":"M4 6h16"}],"$undefined"]}]}]]}]]}]}]}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],"$L5","$L6"]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],"$L7"]}]}]]}]]}],{"children":["about","$L8",{"children":["__PAGE__","$L9",{},null,false]},null,false]},null,false],"$La",false]],"m":"$undefined","G":["$b",[]],"s":false,"S":true}
18:I[9665,[],"ViewportBoundary"]
1a:I[9665,[],"MetadataBoundary"]
1b:"$Sreact.suspense"
5:["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}]
6:["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]
7:["$","footer",null,{"className":"border-t bg-background","children":["$","div",null,{"className":"container mx-auto px-4 py-12","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-8","children":[["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"text-2xl font-bold text-primary","children":"HFX Eats"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","div",null,{"className":"flex space-x-4","children":[["$","a",null,{"href":"https://twitter.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Twitter","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-twitter h-5 w-5","aria-hidden":"true","children":[["$","path","pff0z6",{"d":"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"}],"$undefined"]}]}],["$","a",null,{"href":"https://instagram.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Instagram","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-instagram h-5 w-5","aria-hidden":"true","children":[["$","rect","2e1cvw",{"width":"20","height":"20","x":"2","y":"2","rx":"5","ry":"5"}],["$","path","9exkf1",{"d":"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"}],["$","line","r4j83e",{"x1":"17.5","x2":"17.51","y1":"6.5","y2":"6.5"}],"$undefined"]}]}],["$","a",null,{"href":"https://facebook.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Facebook","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-facebook h-5 w-5","aria-hidden":"true","children":[["$","path","1jg4f8",{"d":"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"}],"$undefined"]}]}],["$","a",null,{"href":"mailto:<EMAIL>","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Email","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-mail h-5 w-5","aria-hidden":"true","children":[["$","path","132q7q",{"d":"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"}],["$","rect","izxlao",{"x":"2","y":"4","width":"20","height":"16","rx":"2"}],"$undefined"]}]}]]}]]}],["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"Reviews"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/","className":"text-muted-foreground hover:text-primary transition-colors","children":"All Reviews"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?rating=5","className":"text-muted-foreground hover:text-primary transition-colors","children":"5-Star Reviews"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?cuisine=seafood","className":"text-muted-foreground hover:text-primary transition-colors","children":"Seafood"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?price=$$$","className":"text-muted-foreground hover:text-primary transition-colors","children":"Fine Dining"}]}]]}]]}],["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"Neighbourhoods"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/downtown/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Downtown"}]}],"$Lc","$Ld","$Le"]}]]}],"$Lf"]}],"$L10"]}]}]
8:["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
9:["$","$1","c",{"children":[["$","div",null,{"className":"container mx-auto px-4 py-12 max-w-4xl","children":["$","div",null,{"className":"space-y-16","children":[["$","div",null,{"className":"text-center space-y-6","children":[["$","h1",null,{"className":"text-4xl md:text-5xl font-bold","children":"About HFX Eats"}],["$","p",null,{"className":"text-xl text-muted-foreground max-w-3xl mx-auto","children":"Halifax's most trusted restaurant review blog, dedicated to uncovering the best dining experiences across Nova Scotia's vibrant capital city."}]]}],["$","section",null,{"className":"space-y-6","children":[["$","h2",null,{"className":"text-3xl font-bold","children":"Our Mission"}],["$","div",null,{"className":"prose prose-lg max-w-none dark:prose-invert","children":[["$","p",null,{"children":[["$","strong",null,{"children":"Quick Take:"}]," We're Halifax locals with deep culinary knowledge and years of restaurant experience. Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers."]}],["$","p",null,{"children":"Founded in 2020, HFX Eats emerged from a simple belief: Halifax deserves thoughtful, honest restaurant criticism that celebrates our unique Maritime culinary identity while holding establishments to high standards. We're not just reviewers – we're passionate advocates for Halifax's incredible food scene."}]]}]]}],["$","section",null,{"className":"grid md:grid-cols-4 gap-8 py-8 border-y","children":[["$","div",null,{"className":"text-center space-y-2","children":[["$","div",null,{"className":"flex justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-star w-8 h-8 text-yellow-400 fill-current","aria-hidden":"true","children":[["$","path","r04s7s",{"d":"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"}],"$undefined"]}]}],["$","div",null,{"className":"text-2xl font-bold","children":"200+"}],["$","div",null,{"className":"text-sm text-muted-foreground","children":"Restaurants Reviewed"}]]}],["$","div",null,{"className":"text-center space-y-2","children":[["$","div",null,{"className":"flex justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin w-8 h-8 text-primary","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}]}],["$","div",null,{"className":"text-2xl font-bold","children":"5+"}],["$","div",null,{"className":"text-sm text-muted-foreground","children":"Years in Halifax"}]]}],["$","div",null,{"className":"text-center space-y-2","children":[["$","div",null,{"className":"flex justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock w-8 h-8 text-green-600","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}]}],["$","div",null,{"className":"text-2xl font-bold","children":"Weekly"}],["$","div",null,{"className":"text-sm text-muted-foreground","children":"New Reviews"}]]}],["$","div",null,{"className":"text-center space-y-2","children":[["$","div",null,{"className":"flex justify-center","children":"$L11"}],"$L12","$L13"]}]]}],"$L14","$L15","$L16"]}]}],null,"$L17"]}]
a:["$","$1","h",{"children":[null,[["$","$L18",null,{"children":"$L19"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L1a",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1b",null,{"fallback":null,"children":"$L1c"}]}]}]]}]
1d:I[9665,[],"OutletBoundary"]
1f:I[4911,[],"AsyncMetadataOutlet"]
c:["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/north-end/","className":"text-muted-foreground hover:text-primary transition-colors","children":"North End"}]}]
d:["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/south-end/","className":"text-muted-foreground hover:text-primary transition-colors","children":"South End"}]}]
e:["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/dartmouth/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Dartmouth"}]}]
f:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"About"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/about/","className":"text-muted-foreground hover:text-primary transition-colors","children":"About Us"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/editorial-guidelines/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Editorial Guidelines"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/contact/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Contact"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/privacy/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Privacy Policy"}]}]]}]]}]
10:["$","div",null,{"className":"mt-8 pt-8 border-t text-center text-sm text-muted-foreground","children":["$","p",null,{"children":["© ",2025," ","HFX Eats",". All rights reserved. Made with ❤️ in Halifax, Nova Scotia."]}]}]
11:["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users w-8 h-8 text-blue-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]
12:["$","div",null,{"className":"text-2xl font-bold","children":"50K+"}]
13:["$","div",null,{"className":"text-sm text-muted-foreground","children":"Monthly Readers"}]
14:["$","section",null,{"className":"space-y-8","children":[["$","h2",null,{"className":"text-3xl font-bold","children":"Meet the Team"}],["$","div",null,{"className":"grid md:grid-cols-2 gap-8","children":[["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"aspect-square bg-muted rounded-lg flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users w-16 h-16 text-muted-foreground","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]}],["$","div",null,{"className":"space-y-2","children":[["$","h3",null,{"className":"text-xl font-semibold","children":"Halifax Food Critic"}],["$","p",null,{"className":"text-muted-foreground","children":"Lead Reviewer & Founder"}],["$","p",null,{"className":"text-sm","children":"A Halifax native with 15+ years in the culinary industry, including stints at some of the city's most respected restaurants. Holds a culinary arts degree and has been writing about food for over a decade."}]]}]]}],["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"aspect-square bg-muted rounded-lg flex items-center justify-center","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users w-16 h-16 text-muted-foreground","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]}],["$","div",null,{"className":"space-y-2","children":[["$","h3",null,{"className":"text-xl font-semibold","children":"Maritime Dining Expert"}],["$","p",null,{"className":"text-muted-foreground","children":"Contributing Writer"}],["$","p",null,{"className":"text-sm","children":"Specializes in Maritime cuisine and local sourcing. Former chef turned food writer with deep connections to Halifax's farming and fishing communities. Brings insider knowledge of seasonal ingredients and traditional techniques."}]]}]]}]]}]]}]
15:["$","section",null,{"className":"space-y-6","children":[["$","h2",null,{"className":"text-3xl font-bold","children":"Our Values"}],["$","div",null,{"className":"grid md:grid-cols-3 gap-6","children":[["$","div",null,{"className":"space-y-3","children":[["$","h3",null,{"className":"text-lg font-semibold","children":"Authenticity"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Every review is based on genuine dining experiences. We pay for our meals and never accept compensation that could influence our opinions."}]]}],["$","div",null,{"className":"space-y-3","children":[["$","h3",null,{"className":"text-lg font-semibold","children":"Local Focus"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"We celebrate Halifax's unique culinary identity while supporting local restaurants, farmers, and food producers who make our city's dining scene special."}]]}],["$","div",null,{"className":"space-y-3","children":[["$","h3",null,{"className":"text-lg font-semibold","children":"Constructive Criticism"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback that can help establishments improve."}]]}]]}]]}]
16:["$","section",null,{"className":"bg-muted/50 rounded-lg p-8 text-center space-y-4","children":[["$","h2",null,{"className":"text-2xl font-bold","children":"Get in Touch"}],["$","p",null,{"className":"text-muted-foreground","children":"Have a restaurant recommendation or want to collaborate? We'd love to hear from you."}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","a",null,{"href":"mailto:<EMAIL>","className":"inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90","children":"Email Us"}],["$","a",null,{"href":"https://instagram.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground","children":"Follow on Instagram"}]]}]]}]
17:["$","$L1d",null,{"children":["$L1e",["$","$L1f",null,{"promise":"$@20"}]]}]
19:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
1e:null
21:I[8175,[],"IconMark"]
20:{"metadata":[["$","title","0",{"children":"About Us | HFX Eats"}],["$","meta","1",{"name":"description","content":"Learn about HFX Eats, Halifax&apos;s premier restaurant review blog. Meet our team of local food critics and discover our commitment to authentic dining experiences."}],["$","link","2",{"rel":"author","href":"https://hfxeats.com/about"}],["$","meta","3",{"name":"author","content":"Halifax Food Critic"}],["$","meta","4",{"name":"keywords","content":"Halifax restaurants,Nova Scotia dining,restaurant reviews,Halifax food,dining guide,HFX eats,Halifax food critic"}],["$","meta","5",{"name":"creator","content":"Halifax Food Critic"}],["$","meta","6",{"name":"publisher","content":"HFX Eats"}],["$","meta","7",{"name":"robots","content":"index, follow"}],["$","meta","8",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","9",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","10",{"property":"og:title","content":"HFX Eats"}],["$","meta","11",{"property":"og:description","content":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","meta","12",{"property":"og:url","content":"https://hfxeats.com/"}],["$","meta","13",{"property":"og:site_name","content":"HFX Eats"}],["$","meta","14",{"property":"og:locale","content":"en_CA"}],["$","meta","15",{"property":"og:image","content":"https://hfxeats.com/og-image.jpg"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"HFX Eats"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:creator","content":"@hfxeats"}],["$","meta","22",{"name":"twitter:title","content":"HFX Eats"}],["$","meta","23",{"name":"twitter:description","content":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","meta","24",{"name":"twitter:image","content":"https://hfxeats.com/og-image.jpg"}],["$","link","25",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L21","26",{}]],"error":null,"digest":"$undefined"}
1c:"$20:metadata"
