(()=>{var a={};a.id=931,a.ids=[931],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4128:(a,b,c)=>{"use strict";function d(a){throw Object.defineProperty(Error("cacheLife() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E627",enumerable:!1,configurable:!0})}Object.defineProperty(b,"F",{enumerable:!0,get:function(){return d}}),c(29294),c(63033)},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19610:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getSortedRouteObjects:function(){return e},getSortedRoutes:function(){return d}});class c{insert(a){this._insert(a.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(a){void 0===a&&(a="/");let b=[...this.children.keys()].sort();null!==this.slugName&&b.splice(b.indexOf("[]"),1),null!==this.restSlugName&&b.splice(b.indexOf("[...]"),1),null!==this.optionalRestSlugName&&b.splice(b.indexOf("[[...]]"),1);let c=b.map(b=>this.children.get(b)._smoosh(""+a+b+"/")).reduce((a,b)=>[...a,...b],[]);if(null!==this.slugName&&c.push(...this.children.get("[]")._smoosh(a+"["+this.slugName+"]/")),!this.placeholder){let b="/"===a?"/":a.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+b+'" and "'+b+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});c.unshift(b)}return null!==this.restSlugName&&c.push(...this.children.get("[...]")._smoosh(a+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&c.push(...this.children.get("[[...]]")._smoosh(a+"[[..."+this.optionalRestSlugName+"]]/")),c}_insert(a,b,d){if(0===a.length){this.placeholder=!1;return}if(d)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let e=a[0];if(e.startsWith("[")&&e.endsWith("]")){let c=e.slice(1,-1),g=!1;if(c.startsWith("[")&&c.endsWith("]")&&(c=c.slice(1,-1),g=!0),c.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+c+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(c.startsWith("...")&&(c=c.substring(3),d=!0),c.startsWith("[")||c.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+c+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(c.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+c+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function f(a,c){if(null!==a&&a!==c)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+a+"' !== '"+c+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});b.forEach(a=>{if(a===c)throw Object.defineProperty(Error('You cannot have the same slug name "'+c+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(a.replace(/\W/g,"")===e.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+a+'" and "'+c+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),b.push(c)}if(d)if(g){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+a[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});f(this.optionalRestSlugName,c),this.optionalRestSlugName=c,e="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+a[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});f(this.restSlugName,c),this.restSlugName=c,e="[...]"}else{if(g)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+a[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});f(this.slugName,c),this.slugName=c,e="[]"}}this.children.has(e)||this.children.set(e,new c),this.children.get(e)._insert(a.slice(1),b,d)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function d(a){let b=new c;return a.forEach(a=>b.insert(a)),b.smoosh()}function e(a,b){let c={},e=[];for(let d=0;d<a.length;d++){let f=b(a[d]);c[f]=d,e[d]=f}return d(e).map(b=>a[c[b]])}},23496:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getSortedRouteObjects:function(){return d.getSortedRouteObjects},getSortedRoutes:function(){return d.getSortedRoutes},isDynamicRoute:function(){return e.isDynamicRoute}});let d=c(19610),e=c(49022)},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30826:(a,b,c)=>{"use strict";Object.defineProperty(b,"e",{enumerable:!0,get:function(){return k}});let d=c(46143),e=c(37719),f=c(29294),g=c(63033),h=c(43365),i=0;async function j(a,b,c,e,f,g,i){await b.set(c,{kind:h.CachedRouteKind.FETCH,data:{headers:{},body:JSON.stringify(a),status:200,url:""},revalidate:"number"!=typeof f?d.CACHE_ONE_YEAR:f},{fetchCache:!0,tags:e,fetchIdx:g,fetchUrl:i})}function k(a,b,c={}){if(0===c.revalidate)throw Object.defineProperty(Error(`Invariant revalidate: 0 can not be passed to unstable_cache(), must be "false" or "> 0" ${a.toString()}`),"__NEXT_ERROR_CODE",{value:"E57",enumerable:!1,configurable:!0});let d=c.tags?(0,e.validateTags)(c.tags,`unstable_cache ${a.toString()}`):[];(0,e.validateRevalidate)(c.revalidate,`unstable_cache ${a.name||a.toString()}`);let l=`${a.toString()}-${Array.isArray(b)&&b.join(",")}`;return async(...b)=>{let e=f.workAsyncStorage.getStore(),k=g.workUnitAsyncStorage.getStore(),m=(null==e?void 0:e.incrementalCache)||globalThis.__incrementalCache;if(!m)throw Object.defineProperty(Error(`Invariant: incrementalCache missing in unstable_cache ${a.toString()}`),"__NEXT_ERROR_CODE",{value:"E469",enumerable:!1,configurable:!0});let n=k&&"prerender"===k.type?k.cacheSignal:null;n&&n.beginRead();try{let f=k&&"request"===k.type?k:void 0,n=(null==f?void 0:f.url.pathname)??(null==e?void 0:e.route)??"",o=new URLSearchParams((null==f?void 0:f.url.search)??""),p=[...o.keys()].sort((a,b)=>a.localeCompare(b)).map(a=>`${a}=${o.get(a)}`).join("&"),q=`${l}-${JSON.stringify(b)}`,r=await m.generateCacheKey(q),s=`unstable_cache ${n}${p.length?"?":""}${p} ${a.name?` ${a.name}`:r}`,t=(e?e.nextFetchId:i)??1,u=null==k?void 0:k.implicitTags,v={type:"unstable-cache",phase:"render",implicitTags:u,draftMode:k&&e&&(0,g.getDraftModeProviderForCacheScope)(e,k)};if(e){if(e.nextFetchId=t+1,k&&("cache"===k.type||"prerender"===k.type||"prerender-ppr"===k.type||"prerender-legacy"===k.type)){"number"==typeof c.revalidate&&(k.revalidate<c.revalidate||(k.revalidate=c.revalidate));let a=k.tags;if(null===a)k.tags=d.slice();else for(let b of d)a.includes(b)||a.push(b)}if(!(k&&"unstable-cache"===k.type)&&"force-no-store"!==e.fetchCache&&!e.isOnDemandRevalidate&&!m.isOnDemandRevalidate&&!e.isDraftMode){let f=await m.get(r,{kind:h.IncrementalCacheKind.FETCH,revalidate:c.revalidate,tags:d,softTags:null==u?void 0:u.tags,fetchIdx:t,fetchUrl:s});if(f&&f.value)if(f.value.kind!==h.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${q}`);else{let h=void 0!==f.value.data.body?JSON.parse(f.value.data.body):void 0;return f.isStale&&(e.pendingRevalidates||(e.pendingRevalidates={}),e.pendingRevalidates[q]=g.workUnitAsyncStorage.run(v,a,...b).then(a=>j(a,m,r,d,c.revalidate,t,s)).catch(a=>console.error(`revalidating cache with key: ${q}`,a))),h}}let f=await g.workUnitAsyncStorage.run(v,a,...b);return e.isDraftMode||(e.pendingRevalidates||(e.pendingRevalidates={}),e.pendingRevalidates[q]=j(f,m,r,d,c.revalidate,t,s)),f}{if(i+=1,!m.isOnDemandRevalidate){let a=await m.get(r,{kind:h.IncrementalCacheKind.FETCH,revalidate:c.revalidate,tags:d,fetchIdx:t,fetchUrl:s,softTags:null==u?void 0:u.tags});if(a&&a.value){if(a.value.kind!==h.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${q}`);else if(!a.isStale)return void 0!==a.value.data.body?JSON.parse(a.value.data.body):void 0}}let e=await g.workUnitAsyncStorage.run(v,a,...b);return await j(e,m,r,d,c.revalidate,t,s),e}}finally{n&&n.endRead()}}}},35499:(a,b)=>{"use strict";function c(a){return"("===a[0]&&a.endsWith(")")}function d(a){return a.startsWith("@")&&"@children"!==a}function e(a,b){if(a.includes(f)){let a=JSON.stringify(b);return"{}"!==a?f+"?"+a:f}return a}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DEFAULT_SEGMENT_KEY:function(){return g},PAGE_SEGMENT_KEY:function(){return f},addSearchParamsIfPageSegment:function(){return e},isGroupSegment:function(){return c},isParallelRouteSegment:function(){return d}});let f="__PAGE__",g="__DEFAULT__"},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49022:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isDynamicRoute",{enumerable:!0,get:function(){return g}});let d=c(71437),e=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,f=/\/\[[^/]+\](?=\/|$)/;function g(a,b){return(void 0===b&&(b=!0),(0,d.isInterceptionRouteAppPath)(a)&&(a=(0,d.extractInterceptionRouteInformation)(a).interceptedRoute),b)?f.test(a):e.test(a)}},53382:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>C,patchFetch:()=>B,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>z});var d={};c.r(d),c.d(d,{POST:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(62351);async function w(a){if(a.nextUrl.searchParams.get("secret")!==process.env.REVALIDATION_SECRET)return u.NextResponse.json({message:"Invalid secret"},{status:401});try{let{path:b,tag:c}=await a.json();if(b)return(0,v.revalidatePath)(b),u.NextResponse.json({revalidated:!0,path:b,now:Date.now()});if(c)return(0,v.revalidateTag)(c),u.NextResponse.json({revalidated:!0,tag:c,now:Date.now()});return(0,v.revalidatePath)("/"),(0,v.revalidatePath)("/reviews"),u.NextResponse.json({revalidated:!0,paths:["/","/reviews"],now:Date.now()})}catch(a){return u.NextResponse.json({message:"Error revalidating",error:a instanceof Error?a.message:"Unknown error"},{status:500})}}let x=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/revalidate/route",pathname:"/api/revalidate",filename:"route",bundlePath:"app/api/revalidate/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/api/revalidate/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:y,workUnitAsyncStorage:z,serverHooks:A}=x;function B(){return(0,g.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:z})}async function C(a,b,c){var d;let e="/api/revalidate/route";"/index"===e&&(e="/");let g=await x.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||x.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===x.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>x.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>x.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await x.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await x.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await x.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},62351:(a,b,c)=>{let d={unstable_cache:c(30826).e,revalidateTag:c(83518).revalidateTag,revalidatePath:c(83518).revalidatePath,unstable_expireTag:c(83518).unstable_expireTag,unstable_expirePath:c(83518).unstable_expirePath,unstable_noStore:c(68553).M,unstable_cacheLife:c(4128).F,unstable_cacheTag:c(72932).z};a.exports=d,b.unstable_cache=d.unstable_cache,b.revalidatePath=d.revalidatePath,b.revalidateTag=d.revalidateTag,b.unstable_expireTag=d.unstable_expireTag,b.unstable_expirePath=d.unstable_expirePath,b.unstable_noStore=d.unstable_noStore,b.unstable_cacheLife=d.unstable_cacheLife,b.unstable_cacheTag=d.unstable_cacheTag},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68553:(a,b,c)=>{"use strict";Object.defineProperty(b,"M",{enumerable:!0,get:function(){return g}});let d=c(29294),e=c(63033),f=c(84971);function g(){let a=d.workAsyncStorage.getStore(),b=e.workUnitAsyncStorage.getStore();if(a){if(!a.forceStatic){if(a.isUnstableNoStore=!0,b)switch(b.type){case"prerender":case"prerender-client":return}(0,f.markCurrentScopeAsDynamic)(a,b,"unstable_noStore()")}}}},71437:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{INTERCEPTION_ROUTE_MARKERS:function(){return e},extractInterceptionRouteInformation:function(){return g},isInterceptionRouteAppPath:function(){return f}});let d=c(74722),e=["(..)(..)","(.)","(..)","(...)"];function f(a){return void 0!==a.split("/").find(a=>e.find(b=>a.startsWith(b)))}function g(a){let b,c,f;for(let d of a.split("/"))if(c=e.find(a=>d.startsWith(a))){[b,f]=a.split(c,2);break}if(!b||!c||!f)throw Object.defineProperty(Error("Invalid interception route: "+a+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(b=(0,d.normalizeAppPath)(b),c){case"(.)":f="/"===b?"/"+f:b+"/"+f;break;case"(..)":if("/"===b)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});f=b.split("/").slice(0,-1).concat(f).join("/");break;case"(...)":f="/"+f;break;case"(..)(..)":let g=b.split("/");if(g.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});f=g.slice(0,-2).concat(f).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:b,interceptedRoute:f}}},72932:(a,b,c)=>{"use strict";function d(...a){throw Object.defineProperty(Error("cacheTag() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E628",enumerable:!1,configurable:!0})}Object.defineProperty(b,"z",{enumerable:!0,get:function(){return d}}),c(63033),c(37719)},74722:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{normalizeAppPath:function(){return f},normalizeRscURL:function(){return g}});let d=c(85531),e=c(35499);function f(a){return(0,d.ensureLeadingSlash)(a.split("/").reduce((a,b,c,d)=>!b||(0,e.isGroupSegment)(b)||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b,""))}function g(a){return a.replace(/\.rsc($|\?)/,"$1")}},78335:()=>{},83518:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{revalidatePath:function(){return n},revalidateTag:function(){return k},unstable_expirePath:function(){return l},unstable_expireTag:function(){return m}});let d=c(84971),e=c(23496),f=c(46143),g=c(29294),h=c(63033),i=c(98479),j=c(71617);function k(a){return o([a],`revalidateTag ${a}`)}function l(a,b){if(a.length>f.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: expirePath received "${a}" which exceeded max length of ${f.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`);let c=`${f.NEXT_CACHE_IMPLICIT_TAG_ID}${a}`;return b?c+=`${c.endsWith("/")?"":"/"}${b}`:(0,e.isDynamicRoute)(a)&&console.warn(`Warning: a dynamic page path "${a}" was passed to "expirePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`),o([c],`unstable_expirePath ${a}`)}function m(...a){return o(a,`unstable_expireTag ${a.join(", ")}`)}function n(a,b){if(a.length>f.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: revalidatePath received "${a}" which exceeded max length of ${f.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`);let c=`${f.NEXT_CACHE_IMPLICIT_TAG_ID}${a}`;return b?c+=`${c.endsWith("/")?"":"/"}${b}`:(0,e.isDynamicRoute)(a)&&console.warn(`Warning: a dynamic page path "${a}" was passed to "revalidatePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`),o([c],`revalidatePath ${a}`)}function o(a,b){let c=g.workAsyncStorage.getStore();if(!c||!c.incrementalCache)throw Object.defineProperty(Error(`Invariant: static generation store missing in ${b}`),"__NEXT_ERROR_CODE",{value:"E263",enumerable:!1,configurable:!0});let e=h.workUnitAsyncStorage.getStore();if(e){if("cache"===e.type)throw Object.defineProperty(Error(`Route ${c.route} used "${b}" inside a "use cache" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E181",enumerable:!1,configurable:!0});if("unstable-cache"===e.type)throw Object.defineProperty(Error(`Route ${c.route} used "${b}" inside a function cached with "unstable_cache(...)" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E306",enumerable:!1,configurable:!0});if("render"===e.phase)throw Object.defineProperty(Error(`Route ${c.route} used "${b}" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E7",enumerable:!1,configurable:!0});switch(e.type){case"prerender":let a=Object.defineProperty(Error(`Route ${c.route} used ${b} without first calling \`await connection()\`.`),"__NEXT_ERROR_CODE",{value:"E406",enumerable:!1,configurable:!0});(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c.route,b,a,e);break;case"prerender-client":throw Object.defineProperty(new j.InvariantError(`${b} must not be used within a client component. Next.js should be preventing ${b} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,d.postponeWithTracking)(c.route,b,e.dynamicTracking);break;case"prerender-legacy":e.revalidate=0;let f=Object.defineProperty(new i.DynamicServerError(`Route ${c.route} couldn't be rendered statically because it used \`${b}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.dynamicUsageDescription=b,c.dynamicUsageStack=f.stack,f}}for(let b of(c.pendingRevalidatedTags||(c.pendingRevalidatedTags=[]),a))c.pendingRevalidatedTags.includes(b)||c.pendingRevalidatedTags.push(b);c.pathWasRevalidated=!0}},85531:(a,b)=>{"use strict";function c(a){return a.startsWith("/")?a:"/"+a}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"ensureLeadingSlash",{enumerable:!0,get:function(){return c}})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[366,55],()=>b(b.s=53382));module.exports=c})();