1:"$Sreact.fragment"
2:I[6874,["874","static/chunks/874-437a265a67d6cfee.js","63","static/chunks/63-5d41f10033d3d1e1.js","974","static/chunks/app/page-13da5b9205cc0e68.js"],""]
3:I[7555,[],""]
4:I[1295,[],""]
10:I[8393,[],""]
:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/96f6630407e158fb.css","style"]
0:{"P":null,"b":"qyUfQkHXKXNI1T-yC0ITn","p":"","c":["","_not-found",""],"i":false,"f":[[["",{"children":["/_not-found",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/96f6630407e158fb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"LocalBusiness\",\"@id\":\"https://hfxeats.com/#localbusiness\",\"name\":\"HFX Eats\",\"description\":\"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\",\"url\":\"https://hfxeats.com\",\"address\":{\"@type\":\"PostalAddress\",\"addressLocality\":\"Halifax\",\"addressRegion\":\"NS\",\"addressCountry\":\"CA\"},\"areaServed\":{\"@type\":\"City\",\"name\":\"Halifax\",\"addressRegion\":\"NS\",\"addressCountry\":\"CA\"},\"knowsAbout\":[\"Restaurant Reviews\",\"Halifax Dining\",\"Nova Scotia Restaurants\",\"Food Criticism\",\"Culinary Experiences\"]}"}}]}],["$","body",null,{"className":"__className_e8ce0c","children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","header",null,{"className":"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"flex h-16 items-center justify-between","children":[["$","$L2",null,{"href":"/","className":"flex items-center space-x-2","children":["$","div",null,{"className":"text-2xl font-bold text-primary","children":"HFX Eats"}]}],["$","nav",null,{"className":"hidden md:flex items-center space-x-8","children":[["$","$L2",null,{"href":"/reviews/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Reviews"}],["$","$L2",null,{"href":"/guides/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Guides"}],["$","$L2",null,{"href":"/neighbourhoods/","className":"text-sm font-medium hover:text-primary transition-colors","children":"Neighbourhoods"}],["$","$L2",null,{"href":"/about/","className":"text-sm font-medium hover:text-primary transition-colors","children":"About"}]]}],["$","div",null,{"className":"flex items-center space-x-4","children":[["$","button",null,{"className":"p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Search","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-search h-4 w-4","aria-hidden":"true","children":[["$","path","14j7rj",{"d":"m21 21-4.34-4.34"}],["$","circle","4ej97u",{"cx":"11","cy":"11","r":"8"}],"$undefined"]}]}],["$","button",null,{"className":"md:hidden p-2 hover:bg-accent rounded-md transition-colors","aria-label":"Menu","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-menu h-4 w-4","aria-hidden":"true","children":[["$","path","1lakjw",{"d":"M4 12h16"}],["$","path","19g7jn",{"d":"M4 18h16"}],["$","path","1o0s65",{"d":"M4 6h16"}],"$undefined"]}]}]]}]]}]}]}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"border-t bg-background","children":["$","div",null,{"className":"container mx-auto px-4 py-12","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-8","children":[["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"text-2xl font-bold text-primary","children":"HFX Eats"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","div",null,{"className":"flex space-x-4","children":["$L5","$L6","$L7","$L8"]}]]}],"$L9","$La","$Lb"]}],"$Lc"]}]}]]}]}]]}]]}],{"children":["/_not-found","$Ld",{"children":["__PAGE__","$Le",{},null,false]},null,false]},null,false],"$Lf",false]],"m":"$undefined","G":["$10",[]],"s":false,"S":true}
11:I[9665,[],"OutletBoundary"]
13:I[4911,[],"AsyncMetadataOutlet"]
15:I[9665,[],"ViewportBoundary"]
17:I[9665,[],"MetadataBoundary"]
18:"$Sreact.suspense"
5:["$","a",null,{"href":"https://twitter.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Twitter","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-twitter h-5 w-5","aria-hidden":"true","children":[["$","path","pff0z6",{"d":"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"}],"$undefined"]}]}]
6:["$","a",null,{"href":"https://instagram.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Instagram","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-instagram h-5 w-5","aria-hidden":"true","children":[["$","rect","2e1cvw",{"width":"20","height":"20","x":"2","y":"2","rx":"5","ry":"5"}],["$","path","9exkf1",{"d":"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"}],["$","line","r4j83e",{"x1":"17.5","x2":"17.51","y1":"6.5","y2":"6.5"}],"$undefined"]}]}]
7:["$","a",null,{"href":"https://facebook.com/hfxeats","target":"_blank","rel":"noopener noreferrer","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Facebook","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-facebook h-5 w-5","aria-hidden":"true","children":[["$","path","1jg4f8",{"d":"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"}],"$undefined"]}]}]
8:["$","a",null,{"href":"mailto:<EMAIL>","className":"text-muted-foreground hover:text-primary transition-colors","aria-label":"Email","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-mail h-5 w-5","aria-hidden":"true","children":[["$","path","132q7q",{"d":"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"}],["$","rect","izxlao",{"x":"2","y":"4","width":"20","height":"16","rx":"2"}],"$undefined"]}]}]
9:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"Reviews"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/","className":"text-muted-foreground hover:text-primary transition-colors","children":"All Reviews"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?rating=5","className":"text-muted-foreground hover:text-primary transition-colors","children":"5-Star Reviews"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?cuisine=seafood","className":"text-muted-foreground hover:text-primary transition-colors","children":"Seafood"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/reviews/?price=$$$","className":"text-muted-foreground hover:text-primary transition-colors","children":"Fine Dining"}]}]]}]]}]
a:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"Neighbourhoods"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/downtown/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Downtown"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/north-end/","className":"text-muted-foreground hover:text-primary transition-colors","children":"North End"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/south-end/","className":"text-muted-foreground hover:text-primary transition-colors","children":"South End"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/neighbourhoods/dartmouth/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Dartmouth"}]}]]}]]}]
b:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"text-sm font-semibold","children":"About"}],["$","ul",null,{"className":"space-y-2 text-sm","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/about/","className":"text-muted-foreground hover:text-primary transition-colors","children":"About Us"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/editorial-guidelines/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Editorial Guidelines"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/contact/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Contact"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/privacy/","className":"text-muted-foreground hover:text-primary transition-colors","children":"Privacy Policy"}]}]]}]]}]
c:["$","div",null,{"className":"mt-8 pt-8 border-t text-center text-sm text-muted-foreground","children":["$","p",null,{"children":["© ",2025," ","HFX Eats",". All rights reserved. Made with ❤️ in Halifax, Nova Scotia."]}]}]
d:["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
e:["$","$1","c",{"children":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],null,["$","$L11",null,{"children":["$L12",["$","$L13",null,{"promise":"$@14"}]]}]]}]
f:["$","$1","h",{"children":[["$","meta",null,{"name":"robots","content":"noindex"}],[["$","$L15",null,{"children":"$L16"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L17",null,{"children":["$","div",null,{"hidden":true,"children":["$","$18",null,{"fallback":null,"children":"$L19"}]}]}]]}]
16:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
12:null
1a:I[8175,[],"IconMark"]
14:{"metadata":[["$","title","0",{"children":"HFX Eats"}],["$","meta","1",{"name":"description","content":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","link","2",{"rel":"author","href":"https://hfxeats.com/about"}],["$","meta","3",{"name":"author","content":"Halifax Food Critic"}],["$","meta","4",{"name":"keywords","content":"Halifax restaurants,Nova Scotia dining,restaurant reviews,Halifax food,dining guide,HFX eats,Halifax food critic"}],["$","meta","5",{"name":"creator","content":"Halifax Food Critic"}],["$","meta","6",{"name":"publisher","content":"HFX Eats"}],["$","meta","7",{"name":"robots","content":"index, follow"}],["$","meta","8",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","9",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","10",{"property":"og:title","content":"HFX Eats"}],["$","meta","11",{"property":"og:description","content":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","meta","12",{"property":"og:url","content":"https://hfxeats.com/"}],["$","meta","13",{"property":"og:site_name","content":"HFX Eats"}],["$","meta","14",{"property":"og:locale","content":"en_CA"}],["$","meta","15",{"property":"og:image","content":"https://hfxeats.com/og-image.jpg"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"HFX Eats"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:creator","content":"@hfxeats"}],["$","meta","22",{"name":"twitter:title","content":"HFX Eats"}],["$","meta","23",{"name":"twitter:description","content":"Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city."}],["$","meta","24",{"name":"twitter:image","content":"https://hfxeats.com/og-image.jpg"}],["$","link","25",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1a","26",{}]],"error":null,"digest":"$undefined"}
19:"$14:metadata"
