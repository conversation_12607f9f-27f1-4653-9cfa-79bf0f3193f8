(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(37413),e=c(83098),f=c(46784),g=c(49972),h=c(49046),i=c(53148),j=c(4536),k=c.n(j);function l(){let a=(0,e.zu)(),b=a.slice(0,6),c=(0,e.p3)(),j=a.length;return(0,d.jsxs)("div",{className:"space-y-16",children:[(0,d.jsx)("section",{className:"relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold tracking-tight",children:["Halifax's Premier",(0,d.jsx)("span",{className:"text-primary block",children:"Dining Guide"})]}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Authentic restaurant reviews and culinary experiences across Nova Scotia's vibrant capital city. Discover your next favorite meal with our local expertise."})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-8 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(g.A,{className:"w-5 h-5 text-yellow-400 fill-current"}),(0,d.jsxs)("span",{className:"font-medium",children:[c,"/5 Average Rating"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.A,{className:"w-5 h-5 text-primary"}),(0,d.jsxs)("span",{className:"font-medium",children:[j,"+ Halifax Restaurants"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"w-5 h-5 text-green-600"}),(0,d.jsx)("span",{className:"font-medium",children:"Updated Weekly"})]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(k(),{href:"/reviews/",className:"inline-flex items-center justify-center rounded-md bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90",children:"Browse All Reviews"}),(0,d.jsx)(k(),{href:"/guides/",className:"inline-flex items-center justify-center rounded-md border border-input bg-background px-8 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground",children:"Dining Guides"})]})]})}),(0,d.jsxs)("section",{className:"container mx-auto px-4 space-y-12",children:[(0,d.jsxs)("div",{className:"text-center space-y-4",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"Latest Reviews"}),(0,d.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Fresh takes on Halifax's dining scene, from hidden gems to established favorites. Each review is based on multiple visits and honest experiences."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:b.map((a,b)=>(0,d.jsx)(f.v,{review:a,priority:b<3},a.slug))}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)(k(),{href:"/reviews/",className:"inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground",children:"View All Reviews"})})]}),(0,d.jsx)("section",{className:"bg-muted/50 py-16",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"Why Trust HFX Eats?"}),(0,d.jsxs)("p",{className:"text-lg text-muted-foreground max-w-3xl mx-auto",children:[(0,d.jsx)("strong",{children:"Quick Take:"})," We're Halifax locals with deep culinary knowledge and years of restaurant experience. Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers."]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:"100%"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"Authentic Reviews"}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:"No sponsored content or fake reviews"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:"5+"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"Years Experience"}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:"Deep knowledge of Halifax dining"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-primary",children:"Weekly"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"Fresh Content"}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:"Regular updates and new discoveries"})]})]})]})})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29939:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.t.bind(c,49603,23))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46784:(a,b,c)=>{"use strict";c.d(b,{v:()=>n});var d=c(37413),e=c(4536),f=c.n(e),g=c(53384),h=c(49046),i=c(40918),j=c(53148),k=c(78614),l=c(83098),m=c(10974);function n({review:a,className:b,priority:c=!1}){let e=a.image?`/images/${a.slug}/${a.image}`:"/images/placeholder-restaurant.jpg";return(0,d.jsx)("article",{className:(0,m.cn)("group bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow duration-200",b),children:(0,d.jsxs)(f(),{href:`/reviews/${a.slug}/`,className:"block",children:[(0,d.jsxs)("div",{className:"aspect-video relative overflow-hidden rounded-t-lg",children:[(0,d.jsx)(g.default,{src:e,alt:a.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-200",priority:c,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,d.jsx)("div",{className:"absolute top-3 right-3",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full px-2 py-1",children:(0,d.jsx)(k.z,{rating:a.rating,size:"sm",showNumber:!1})})})]}),(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors",children:a.title}),(0,d.jsx)("p",{className:"text-muted-foreground line-clamp-3",children:a.summary})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(h.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"truncate max-w-[120px]",children:a.address.split(",")[0]})]}),(0,d.jsx)("div",{className:"flex items-center gap-1",children:(0,d.jsx)("span",{className:"font-medium",children:a.priceLevel})})]}),(0,d.jsx)(k.z,{rating:a.rating,size:"sm"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground pt-2 border-t",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(i.A,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:(0,l.Yq)(a.datePublished)})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(j.A,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:a.readingTime.text})]})]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.cuisine.split(",").map((a,b)=>(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground",children:a.trim()},b))})]})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78614:(a,b,c)=>{"use strict";c.d(b,{z:()=>g});var d=c(37413),e=c(49972),f=c(10974);function g({rating:a,maxRating:b=5,size:c="md",showNumber:g=!0,className:h}){let i={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,d.jsxs)("div",{className:(0,f.cn)("flex items-center gap-2",h),children:[(0,d.jsx)("div",{className:"flex items-center gap-0.5",children:Array.from({length:b},(b,g)=>{let h=g+1,j=h<=Math.floor(a),k=h===Math.ceil(a)&&a%1!=0;return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(e.A,{className:(0,f.cn)(i[c],"text-gray-300 dark:text-gray-600"),fill:"currentColor"}),(j||k)&&(0,d.jsx)(e.A,{className:(0,f.cn)(i[c],"absolute top-0 left-0 text-yellow-400",k&&"clip-path-half"),fill:"currentColor",style:k?{clipPath:"polygon(0 0, 50% 0, 50% 100%, 0 100%)"}:void 0})]},g)})}),g&&(0,d.jsx)("span",{className:(0,f.cn)("font-medium text-gray-900 dark:text-gray-100",{sm:"text-sm",md:"text-base",lg:"text-lg"}[c]),children:a.toFixed(1)})]})}},79428:a=>{"use strict";a.exports=require("buffer")},81479:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21204)),"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,95968))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,16439)),"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,95968))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},83098:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>r,fQ:()=>q,gi:()=>n,p3:()=>s,rn:()=>o,zu:()=>p});var d=c(29021),e=c.n(d),f=c(33873),g=c.n(f),h=c(99379),i=c.n(h),j=c(20702),k=c.n(j);let l=g().join(process.cwd(),"content"),m=g().join(l,"reviews");function n(){return e().existsSync(m)?e().readdirSync(m).filter(a=>a.endsWith(".mdx")).map(a=>a.replace(/\.mdx$/,"")):[]}function o(a){try{let b=g().join(m,`${a}.mdx`);if(!e().existsSync(b))return null;let c=e().readFileSync(b,"utf8"),{data:d,content:f}=i()(c),h=k()(f);return{slug:a,content:f,readingTime:h,title:d.title||"",summary:d.summary||"",rating:d.rating||0,address:d.address||"",lat:d.lat||0,lng:d.lng||0,priceLevel:d.priceLevel||"",cuisine:d.cuisine||"",datePublished:d.datePublished||"",lastUpdated:d.lastUpdated||d.datePublished||"",image:d.image||"",author:d.author||""}}catch(b){return console.error(`Error reading review ${a}:`,b),null}}function p(){return n().map(a=>o(a)).filter(a=>null!==a).sort((a,b)=>new Date(b.datePublished).getTime()-new Date(a.datePublished).getTime())}function q(a,b=3){let c=o(a);return c?p().filter(b=>b.slug!==a).map(a=>{let b=0;return a.cuisine===c.cuisine&&(b+=3),a.priceLevel===c.priceLevel&&(b+=2),.5>=Math.abs(a.rating-c.rating)&&(b+=1),{review:a,score:b}}).sort((a,b)=>b.score-a.score).slice(0,b).map(a=>a.review):[]}function r(a){return new Date(a).toLocaleDateString("en-CA",{year:"numeric",month:"long",day:"numeric"})}function s(){let a=p();return 0===a.length?0:Math.round(a.reduce((a,b)=>a+b.rating,0)/a.length*10)/10}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90187:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.t.bind(c,46533,23))}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[366,985,675,224],()=>b(b.s=81479));module.exports=c})();