(()=>{var a={};a.id=192,a.ids=[192],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42309:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["editorial-guidelines",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,64929)),"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,95968))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,16439)),"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,95968))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/editorial-guidelines/page",pathname:"/editorial-guidelines",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/editorial-guidelines/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64929:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(26373);let f=(0,e.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),g=(0,e.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),h=(0,e.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),i={title:"Editorial Guidelines",description:"Learn about HFX Eats&apos; editorial standards, review process, and commitment to authentic, unbiased restaurant criticism."};function j(){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:(0,d.jsxs)("div",{className:"space-y-16",children:[(0,d.jsxs)("div",{className:"text-center space-y-6",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold",children:"Editorial Guidelines"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Our commitment to honest, helpful, and authentic restaurant reviews that serve Halifax diners and the local food community."})]}),(0,d.jsxs)("section",{className:"space-y-8",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"Core Principles"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"space-y-4 p-6 border rounded-lg",children:[(0,d.jsx)(f,{className:"w-8 h-8 text-green-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Independence"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"We maintain complete editorial independence. All meals are paid for by HFX Eats, and we never accept free meals, gifts, or compensation that could influence our reviews."})]}),(0,d.jsxs)("div",{className:"space-y-4 p-6 border rounded-lg",children:[(0,d.jsx)(f,{className:"w-8 h-8 text-green-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Authenticity"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Every review reflects genuine dining experiences. We visit restaurants as regular customers and base our opinions on multiple visits when possible."})]}),(0,d.jsxs)("div",{className:"space-y-4 p-6 border rounded-lg",children:[(0,d.jsx)(f,{className:"w-8 h-8 text-green-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Constructive Feedback"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback while celebrating what makes Halifax's food scene special."})]})]})]}),(0,d.jsxs)("section",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"Review Process"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold",children:"1"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Initial Visit"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"We visit restaurants as regular customers, making reservations under personal names. We never identify ourselves as reviewers during the dining experience."})]})]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold",children:"2"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Documentation"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"We take detailed notes during and immediately after each visit, documenting food quality, service, atmosphere, and value. Photos are taken discreetly when appropriate."})]})]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold",children:"3"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Multiple Visits"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"For comprehensive reviews, we aim for multiple visits at different times and days to ensure consistency. Single-visit reviews are clearly noted."})]})]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold",children:"4"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Writing & Review"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Reviews are written within 48 hours of the final visit while memories are fresh. Each review undergoes editorial review for accuracy, fairness, and clarity."})]})]})]})]}),(0,d.jsxs)("section",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"Rating System"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:"5.0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Exceptional"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Outstanding in every aspect. A destination restaurant that defines Halifax dining excellence."})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:"4.0-4.9"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Excellent"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Highly recommended with minor flaws. Consistently delivers great experiences."})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:"3.0-3.9"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Good"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Solid choice with some standout elements. Worth visiting with realistic expectations."})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:"2.0-2.9"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Fair"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Has potential but significant issues prevent recommendation. May improve with time."})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:"1.0-1.9"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold",children:"Poor"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Fundamental problems that significantly impact the dining experience. Not recommended."})]})]})]})]}),(0,d.jsxs)("section",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold",children:"What We Do & Don't Do"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h3",{className:"text-xl font-semibold text-green-600 flex items-center gap-2",children:[(0,d.jsx)(f,{className:"w-5 h-5"}),"We Do"]}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),"Pay for all meals and experiences"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),"Visit restaurants multiple times when possible"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),"Consider context (new restaurants, off nights, etc.)"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),"Update reviews when restaurants make significant changes"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),"Highlight local sourcing and sustainability efforts"]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h3",{className:"text-xl font-semibold text-red-600 flex items-center gap-2",children:[(0,d.jsx)(g,{className:"w-5 h-5"}),"We Don't Do"]}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(g,{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0"}),"Accept free meals or compensation"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(g,{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0"}),"Review restaurants based on single bad experiences"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(g,{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0"}),"Make personal attacks on staff or owners"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(g,{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0"}),"Review restaurants during their first week of operation"]}),(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)(g,{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0"}),"Use AI-generated content or stock photos"]})]})]})]})]}),(0,d.jsxs)("section",{className:"bg-muted/50 rounded-lg p-8 space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h,{className:"w-6 h-6 text-amber-600"}),(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"Corrections & Updates"})]}),(0,d.jsxs)("p",{className:"text-muted-foreground",children:["We strive for accuracy in all our reviews. If you notice an error or if a restaurant has made significant changes since our review, please contact us at ",(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"}),". We will investigate and update our content as appropriate."]}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"All corrections and significant updates are noted at the bottom of reviews with timestamps for transparency."})]})]})})}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[366,985,224],()=>b(b.s=42309));module.exports=c})();