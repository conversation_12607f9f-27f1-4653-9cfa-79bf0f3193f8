(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[112],{875:(e,r,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,3063,23)),Promise.resolve().then(a.bind(a,8160))},8160:(e,r,a)=>{"use strict";a.d(r,{MapEmbed:()=>i});var s=a(5155),t=a(6086),n=a(2596),l=a(9688);function c(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,l.QP)((0,n.$)(r))}var o=a(9509);function i(e){let{lat:r,lng:a,address:n,className:l,height:i="h-64"}=e,d="https://www.google.com/maps/embed/v1/place?key=".concat(o.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,"&q=").concat(r,",").concat(a,"&zoom=15"),m="https://www.google.com/maps?q=".concat(r,",").concat(a);return(0,s.jsxs)("div",{className:c("my-8 space-y-4",l),children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-lg font-semibold",children:[(0,s.jsx)(t.A,{className:"w-5 h-5 text-primary"}),"Location"]}),n&&(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n}),(0,s.jsx)("div",{className:c("relative rounded-lg overflow-hidden border",i),children:o.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY?(0,s.jsx)("iframe",{src:d,width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Map showing location ".concat(n?"of ".concat(n):"at ".concat(r,", ").concat(a))}):(0,s.jsxs)("div",{className:"w-full h-full bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center space-y-4",children:[(0,s.jsx)(t.A,{className:"w-12 h-12 text-gray-400"}),(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Interactive map not available"}),(0,s.jsxs)("a",{href:m,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-primary hover:underline",children:[(0,s.jsx)(t.A,{className:"w-4 h-4"}),"View on Google Maps"]})]})]})}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("a",{href:m,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-sm text-primary hover:underline",children:[(0,s.jsx)(t.A,{className:"w-4 h-4"}),"Open in Google Maps"]})})]})}}},e=>{e.O(0,[874,63,322,441,964,358],()=>e(e.s=875)),_N_E=e.O()}]);