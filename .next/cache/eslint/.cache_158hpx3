[{"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/about/page.tsx": "1", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/api/revalidate/route.ts": "2", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx": "3", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx": "4", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx": "5", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/[slug]/page.tsx": "6", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/page.tsx": "7", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Footer.tsx": "8", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Header.tsx": "9", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/MapEmbed.tsx": "10", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ProsCons.tsx": "11", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RatingStars.tsx": "12", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RelatedReviews.tsx": "13", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ReviewCard.tsx": "14", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/content.ts": "15", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/seo.ts": "16", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/utils.ts": "17"}, {"size": 7846, "mtime": 1753234923529, "results": "18", "hashOfConfig": "19"}, {"size": 1303, "mtime": 1753234658471, "results": "20", "hashOfConfig": "19"}, {"size": 12228, "mtime": 1753234837923, "results": "21", "hashOfConfig": "19"}, {"size": 2321, "mtime": 1753234088422, "results": "22", "hashOfConfig": "19"}, {"size": 5611, "mtime": 1753234211618, "results": "23", "hashOfConfig": "19"}, {"size": 4965, "mtime": 1753234774062, "results": "24", "hashOfConfig": "19"}, {"size": 3412, "mtime": 1753234827270, "results": "25", "hashOfConfig": "19"}, {"size": 5773, "mtime": 1753234125956, "results": "26", "hashOfConfig": "19"}, {"size": 2100, "mtime": 1753234105432, "results": "27", "hashOfConfig": "19"}, {"size": 2548, "mtime": 1753233991561, "results": "28", "hashOfConfig": "19"}, {"size": 1555, "mtime": 1753233977578, "results": "29", "hashOfConfig": "19"}, {"size": 1989, "mtime": 1753233967150, "results": "30", "hashOfConfig": "19"}, {"size": 880, "mtime": 1753234023736, "results": "31", "hashOfConfig": "19"}, {"size": 3441, "mtime": 1753234007262, "results": "32", "hashOfConfig": "19"}, {"size": 4547, "mtime": 1753233951609, "results": "33", "hashOfConfig": "19"}, {"size": 5619, "mtime": 1753233929937, "results": "34", "hashOfConfig": "19"}, {"size": 166, "mtime": 1753233852047, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1twpl2x", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/about/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx", ["87", "88", "89"], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx", ["90", "91", "92", "93", "94"], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/page.tsx", ["95", "96", "97"], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Footer.tsx", ["98", "99"], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Header.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/MapEmbed.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ProsCons.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RatingStars.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RelatedReviews.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ReviewCard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/content.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/seo.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/utils.ts", [], [], {"ruleId": "100", "severity": 2, "message": "101", "line": 51, "column": 53, "nodeType": "102", "messageId": "103", "suggestions": "104"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 175, "column": 62, "nodeType": "102", "messageId": "103", "suggestions": "105"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 210, "column": 23, "nodeType": "102", "messageId": "103", "suggestions": "106"}, {"ruleId": "107", "severity": 1, "message": "108", "line": 3, "column": 10, "nodeType": null, "messageId": "109", "endLine": 3, "endColumn": 21}, {"ruleId": "100", "severity": 2, "message": "101", "line": 20, "column": 22, "nodeType": "102", "messageId": "103", "suggestions": "110"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 24, "column": 87, "nodeType": "102", "messageId": "103", "suggestions": "111"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 66, "column": 35, "nodeType": "102", "messageId": "103", "suggestions": "112"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 97, "column": 46, "nodeType": "102", "messageId": "103", "suggestions": "113"}, {"ruleId": "107", "severity": 1, "message": "114", "line": 5, "column": 10, "nodeType": null, "messageId": "109", "endLine": 5, "endColumn": 20}, {"ruleId": "100", "severity": 2, "message": "101", "line": 29, "column": 46, "nodeType": "102", "messageId": "103", "suggestions": "115"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 79, "column": 15, "nodeType": "102", "messageId": "103", "suggestions": "116"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 18, "column": 22, "nodeType": "102", "messageId": "103", "suggestions": "117"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 18, "column": 113, "nodeType": "102", "messageId": "103", "suggestions": "118"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["119", "120", "121", "122"], ["123", "124", "125", "126"], ["127", "128", "129", "130"], "@typescript-eslint/no-unused-vars", "'RatingStars' is defined but never used.", "unusedVar", ["131", "132", "133", "134"], ["135", "136", "137", "138"], ["139", "140", "141", "142"], ["143", "144", "145", "146"], "'siteConfig' is defined but never used.", ["147", "148", "149", "150"], ["151", "152", "153", "154"], ["155", "156", "157", "158"], ["159", "160", "161", "162"], {"messageId": "163", "data": "164", "fix": "165", "desc": "166"}, {"messageId": "163", "data": "167", "fix": "168", "desc": "169"}, {"messageId": "163", "data": "170", "fix": "171", "desc": "172"}, {"messageId": "163", "data": "173", "fix": "174", "desc": "175"}, {"messageId": "163", "data": "176", "fix": "177", "desc": "166"}, {"messageId": "163", "data": "178", "fix": "179", "desc": "169"}, {"messageId": "163", "data": "180", "fix": "181", "desc": "172"}, {"messageId": "163", "data": "182", "fix": "183", "desc": "175"}, {"messageId": "163", "data": "184", "fix": "185", "desc": "166"}, {"messageId": "163", "data": "186", "fix": "187", "desc": "169"}, {"messageId": "163", "data": "188", "fix": "189", "desc": "172"}, {"messageId": "163", "data": "190", "fix": "191", "desc": "175"}, {"messageId": "163", "data": "192", "fix": "193", "desc": "166"}, {"messageId": "163", "data": "194", "fix": "195", "desc": "169"}, {"messageId": "163", "data": "196", "fix": "197", "desc": "172"}, {"messageId": "163", "data": "198", "fix": "199", "desc": "175"}, {"messageId": "163", "data": "200", "fix": "201", "desc": "166"}, {"messageId": "163", "data": "202", "fix": "203", "desc": "169"}, {"messageId": "163", "data": "204", "fix": "205", "desc": "172"}, {"messageId": "163", "data": "206", "fix": "207", "desc": "175"}, {"messageId": "163", "data": "208", "fix": "209", "desc": "166"}, {"messageId": "163", "data": "210", "fix": "211", "desc": "169"}, {"messageId": "163", "data": "212", "fix": "213", "desc": "172"}, {"messageId": "163", "data": "214", "fix": "215", "desc": "175"}, {"messageId": "163", "data": "216", "fix": "217", "desc": "166"}, {"messageId": "163", "data": "218", "fix": "219", "desc": "169"}, {"messageId": "163", "data": "220", "fix": "221", "desc": "172"}, {"messageId": "163", "data": "222", "fix": "223", "desc": "175"}, {"messageId": "163", "data": "224", "fix": "225", "desc": "166"}, {"messageId": "163", "data": "226", "fix": "227", "desc": "169"}, {"messageId": "163", "data": "228", "fix": "229", "desc": "172"}, {"messageId": "163", "data": "230", "fix": "231", "desc": "175"}, {"messageId": "163", "data": "232", "fix": "233", "desc": "166"}, {"messageId": "163", "data": "234", "fix": "235", "desc": "169"}, {"messageId": "163", "data": "236", "fix": "237", "desc": "172"}, {"messageId": "163", "data": "238", "fix": "239", "desc": "175"}, {"messageId": "163", "data": "240", "fix": "241", "desc": "166"}, {"messageId": "163", "data": "242", "fix": "243", "desc": "169"}, {"messageId": "163", "data": "244", "fix": "245", "desc": "172"}, {"messageId": "163", "data": "246", "fix": "247", "desc": "175"}, {"messageId": "163", "data": "248", "fix": "249", "desc": "166"}, {"messageId": "163", "data": "250", "fix": "251", "desc": "169"}, {"messageId": "163", "data": "252", "fix": "253", "desc": "172"}, {"messageId": "163", "data": "254", "fix": "255", "desc": "175"}, "replaceWithAlt", {"alt": "256"}, {"range": "257", "text": "258"}, "Replace with `&apos;`.", {"alt": "259"}, {"range": "260", "text": "261"}, "Replace with `&lsquo;`.", {"alt": "262"}, {"range": "263", "text": "264"}, "Replace with `&#39;`.", {"alt": "265"}, {"range": "266", "text": "267"}, "Replace with `&rsquo;`.", {"alt": "256"}, {"range": "268", "text": "269"}, {"alt": "259"}, {"range": "270", "text": "271"}, {"alt": "262"}, {"range": "272", "text": "273"}, {"alt": "265"}, {"range": "274", "text": "275"}, {"alt": "256"}, {"range": "276", "text": "277"}, {"alt": "259"}, {"range": "278", "text": "279"}, {"alt": "262"}, {"range": "280", "text": "281"}, {"alt": "265"}, {"range": "282", "text": "283"}, {"alt": "256"}, {"range": "284", "text": "285"}, {"alt": "259"}, {"range": "286", "text": "287"}, {"alt": "262"}, {"range": "288", "text": "289"}, {"alt": "265"}, {"range": "290", "text": "291"}, {"alt": "256"}, {"range": "292", "text": "293"}, {"alt": "259"}, {"range": "294", "text": "295"}, {"alt": "262"}, {"range": "296", "text": "297"}, {"alt": "265"}, {"range": "298", "text": "299"}, {"alt": "256"}, {"range": "300", "text": "301"}, {"alt": "259"}, {"range": "302", "text": "303"}, {"alt": "262"}, {"range": "304", "text": "305"}, {"alt": "265"}, {"range": "306", "text": "307"}, {"alt": "256"}, {"range": "308", "text": "309"}, {"alt": "259"}, {"range": "310", "text": "311"}, {"alt": "262"}, {"range": "312", "text": "313"}, {"alt": "265"}, {"range": "314", "text": "315"}, {"alt": "256"}, {"range": "316", "text": "317"}, {"alt": "259"}, {"range": "318", "text": "319"}, {"alt": "262"}, {"range": "320", "text": "321"}, {"alt": "265"}, {"range": "322", "text": "323"}, {"alt": "256"}, {"range": "324", "text": "325"}, {"alt": "259"}, {"range": "326", "text": "327"}, {"alt": "262"}, {"range": "328", "text": "329"}, {"alt": "265"}, {"range": "330", "text": "331"}, {"alt": "256"}, {"range": "332", "text": "333"}, {"alt": "259"}, {"range": "334", "text": "335"}, {"alt": "262"}, {"range": "336", "text": "337"}, {"alt": "265"}, {"range": "338", "text": "339"}, {"alt": "256"}, {"range": "340", "text": "341"}, {"alt": "259"}, {"range": "342", "text": "343"}, {"alt": "262"}, {"range": "344", "text": "345"}, {"alt": "265"}, {"range": "346", "text": "347"}, "&apos;", [2358, 2568], "\n                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback \n                while celebrating what makes Halifax&apos;s food scene special.\n              ", "&lsquo;", [2358, 2568], "\n                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback \n                while celebrating what makes Halifax&lsquo;s food scene special.\n              ", "&#39;", [2358, 2568], "\n                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback \n                while celebrating what makes Halifax&#39;s food scene special.\n              ", "&rsquo;", [2358, 2568], "\n                Our reviews aim to be helpful to both diners and restaurants. We provide specific, actionable feedback \n                while celebrating what makes Halifax&rsquo;s food scene special.\n              ", [8216, 8237], "What We Do & Don&apos;t Do", [8216, 8237], "What We Do & Don&lsquo;t Do", [8216, 8237], "What We Do & Don&#39;t Do", [8216, 8237], "What We Do & Don&rsquo;t Do", [9985, 10028], "\n                We Don&apos;t Do\n              ", [9985, 10028], "\n                We Don&lsquo;t Do\n              ", [9985, 10028], "\n                We Don&#39;t <PERSON>\n              ", [9985, 10028], "\n                We Don&rsquo;t Do\n              ", [819, 866], "\n              Halifax&apos;s Premier\n              ", [819, 866], "\n              Halifax&lsquo;s Premier\n              ", [819, 866], "\n              Halifax&#39;s Premier\n              ", [819, 866], "\n              Halifax&rsquo;s Premier\n              ", [1016, 1213], "\n              Authentic restaurant reviews and culinary experiences across Nova Scotia&apos;s vibrant capital city.\n              Discover your next favorite meal with our local expertise.\n            ", [1016, 1213], "\n              Authentic restaurant reviews and culinary experiences across Nova Scotia&lsquo;s vibrant capital city.\n              Discover your next favorite meal with our local expertise.\n            ", [1016, 1213], "\n              Authentic restaurant reviews and culinary experiences across Nova Scotia&#39;s vibrant capital city.\n              Discover your next favorite meal with our local expertise.\n            ", [1016, 1213], "\n              Authentic restaurant reviews and culinary experiences across Nova Scotia&rsquo;s vibrant capital city.\n              Discover your next favorite meal with our local expertise.\n            ", [3018, 3199], "\n            Fresh takes on Halifax&apos;s dining scene, from hidden gems to established favorites.\n            Each review is based on multiple visits and honest experiences.\n          ", [3018, 3199], "\n            Fresh takes on Halifax&lsquo;s dining scene, from hidden gems to established favorites.\n            Each review is based on multiple visits and honest experiences.\n          ", [3018, 3199], "\n            Fresh takes on Halifax&#39;s dining scene, from hidden gems to established favorites.\n            Each review is based on multiple visits and honest experiences.\n          ", [3018, 3199], "\n            Fresh takes on Halifax&rsquo;s dining scene, from hidden gems to established favorites.\n            Each review is based on multiple visits and honest experiences.\n          ", [4270, 4524], " We&apos;re Halifax locals with deep culinary knowledge and years of restaurant experience.\n              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.\n            ", [4270, 4524], " We&lsquo;re Halifax locals with deep culinary knowledge and years of restaurant experience.\n              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.\n            ", [4270, 4524], " We&#39;re Halifax locals with deep culinary knowledge and years of restaurant experience.\n              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.\n            ", [4270, 4524], " We&rsquo;re Halifax locals with deep culinary knowledge and years of restaurant experience.\n              Every review reflects genuine visits, honest opinions, and a commitment to helping you discover the best dining experiences our city offers.\n            ", [1174, 1355], "\n          Honest, detailed reviews of Halifax&apos;s dining scene. From hidden gems to established favorites, \n          discover your next great meal with our local expertise.\n        ", [1174, 1355], "\n          Honest, detailed reviews of Halifax&lsquo;s dining scene. From hidden gems to established favorites, \n          discover your next great meal with our local expertise.\n        ", [1174, 1355], "\n          Honest, detailed reviews of Halifax&#39;s dining scene. From hidden gems to established favorites, \n          discover your next great meal with our local expertise.\n        ", [1174, 1355], "\n          Honest, detailed reviews of Halifax&rsquo;s dining scene. From hidden gems to established favorites, \n          discover your next great meal with our local expertise.\n        ", [3259, 3366], "\n            We&apos;re working on bringing you the best Halifax restaurant reviews. Check back soon!\n          ", [3259, 3366], "\n            We&lsquo;re working on bringing you the best Halifax restaurant reviews. Check back soon!\n          ", [3259, 3366], "\n            We&#39;re working on bringing you the best Halifax restaurant reviews. Check back soon!\n          ", [3259, 3366], "\n            We&rsquo;re working on bringing you the best Halifax restaurant reviews. Check back soon!\n          ", [618, 760], "\n              Halifax&apos;s premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\n            ", [618, 760], "\n              Halifax&lsquo;s premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\n            ", [618, 760], "\n              Halifax&#39;s premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\n            ", [618, 760], "\n              Halifax&rsquo;s premier restaurant review blog featuring authentic dining experiences across Nova Scotia's capital city.\n            ", [618, 760], "\n              Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia&apos;s capital city.\n            ", [618, 760], "\n              Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia&lsquo;s capital city.\n            ", [618, 760], "\n              Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia&#39;s capital city.\n            ", [618, 760], "\n              Halifax's premier restaurant review blog featuring authentic dining experiences across Nova Scotia&rsquo;s capital city.\n            "]