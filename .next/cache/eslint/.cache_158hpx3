[{"/Users/<USER>/Documents/augment-projects/hfxeats/src/app/about/page.tsx": "1", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/api/revalidate/route.ts": "2", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx": "3", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx": "4", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx": "5", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/[slug]/page.tsx": "6", "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/page.tsx": "7", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Footer.tsx": "8", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Header.tsx": "9", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/MapEmbed.tsx": "10", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ProsCons.tsx": "11", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RatingStars.tsx": "12", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RelatedReviews.tsx": "13", "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ReviewCard.tsx": "14", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/content.ts": "15", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/seo.ts": "16", "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/utils.ts": "17"}, {"size": 7846, "mtime": 1753234923529, "results": "18", "hashOfConfig": "19"}, {"size": 1303, "mtime": 1753234658471, "results": "20", "hashOfConfig": "19"}, {"size": 12247, "mtime": 1753235251656, "results": "21", "hashOfConfig": "19"}, {"size": 2321, "mtime": 1753234088422, "results": "22", "hashOfConfig": "19"}, {"size": 5576, "mtime": 1753234987784, "results": "23", "hashOfConfig": "19"}, {"size": 4965, "mtime": 1753234774062, "results": "24", "hashOfConfig": "19"}, {"size": 3382, "mtime": 1753235267479, "results": "25", "hashOfConfig": "19"}, {"size": 5783, "mtime": 1753235217844, "results": "26", "hashOfConfig": "19"}, {"size": 2100, "mtime": 1753234105432, "results": "27", "hashOfConfig": "19"}, {"size": 2548, "mtime": 1753233991561, "results": "28", "hashOfConfig": "19"}, {"size": 1555, "mtime": 1753233977578, "results": "29", "hashOfConfig": "19"}, {"size": 1989, "mtime": 1753233967150, "results": "30", "hashOfConfig": "19"}, {"size": 880, "mtime": 1753234023736, "results": "31", "hashOfConfig": "19"}, {"size": 3441, "mtime": 1753234007262, "results": "32", "hashOfConfig": "19"}, {"size": 4547, "mtime": 1753233951609, "results": "33", "hashOfConfig": "19"}, {"size": 5619, "mtime": 1753233929937, "results": "34", "hashOfConfig": "19"}, {"size": 166, "mtime": 1753233852047, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1twpl2x", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/about/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/editorial-guidelines/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/app/reviews/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Footer.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/Header.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/MapEmbed.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ProsCons.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RatingStars.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/RelatedReviews.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/components/ReviewCard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/content.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/seo.ts", [], [], "/Users/<USER>/Documents/augment-projects/hfxeats/src/lib/utils.ts", [], []]