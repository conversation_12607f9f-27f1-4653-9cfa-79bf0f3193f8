{"buildCommand": "npm run build && npm run postbuild", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "functions": {"src/app/api/revalidate/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(css|js|woff|woff2|ttf|otf))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/review/:slug", "destination": "/reviews/:slug", "permanent": true}], "rewrites": [{"source": "/robots.txt", "destination": "/api/robots"}, {"source": "/sitemap.xml", "destination": "/api/sitemap"}]}